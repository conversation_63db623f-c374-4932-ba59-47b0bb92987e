package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.hud.EventSkia2D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.features.module.modules.other.KillerCheck;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import java.util.Arrays;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.ui.skija.utils.SkiaCompatLayer;
import com.leave.ink.ui.skija.fbo.GameFrameBuffer;
import com.leave.ink.ui.skija.fbo.FrameBuffers;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.entity.ItemRenderer;
import com.mojang.blaze3d.platform.Lighting;
import net.minecraft.util.Mth;
import org.joml.Matrix4f;
import org.joml.Vector3f;
import org.joml.Vector4f;
import com.mojang.blaze3d.systems.RenderSystem;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.ui.skija.utils.Skia3DRenderer;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.player.PlayerUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.Projection;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.world.phys.Vec3;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import org.lwjgl.opengl.GL20;
import java.awt.*;
import java.text.DecimalFormat;
import com.leave.ink.ui.skija.font.IconFont;

@ModuleInfo(name = {
        @Text(label = "NameTags", language = Language.English),
        @Text(label = "名字标签", language = Language.Chinese)
}, category = Category.Render)
public class NameTags extends Module {

    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量 ", language = Language.Chinese)
    })
    private final BooleanSetting healthValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Distance", language = Language.English),
            @Text(label = "距离 ", language = Language.Chinese)
    })
    private final BooleanSetting distanceValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Armor", language = Language.English),
            @Text(label = "盔甲 ", language = Language.Chinese)
    })
    private final BooleanSetting armorValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Scale", language = Language.English),
            @Text(label = "大小 ", language = Language.Chinese)
    })
    private final NumberSetting scaleValue = new NumberSetting(1.5F, 1F, 4F, "#.00");
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊 ", language = Language.Chinese)
    })
    private final BooleanSetting blurValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Self", language = Language.English),
            @Text(label = "自己 ", language = Language.Chinese)
    })
    private final BooleanSetting self = new BooleanSetting(false);
    
    @SettingInfo(name = {
            @Text(label = "Dis Mc Name", language = Language.English),
            @Text(label = "禁用原版标签", language = Language.Chinese)
    })
    private final BooleanSetting disableVanilla = new BooleanSetting(true);
    
    @SettingInfo(name = {
            @Text(label = "Skia X Offset", language = Language.English),
            @Text(label = "Skia X 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaXOffset = new NumberSetting(-8F, -50F, 50F, "#.0");
    
    @SettingInfo(name = {
            @Text(label = "Skia Y Offset", language = Language.English),
            @Text(label = "Skia Y 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaYOffset = new NumberSetting(6F, -50F, 50F, "#.0");

    @SettingInfo(name = {
            @Text(label = "UI Style", language = Language.English),
            @Text(label = "UI风格", language = Language.Chinese)
    })
    private final ModeSetting uiStyle = new ModeSetting("Modern", Arrays.asList("Modern", "Classic"));

    @SettingInfo(name = {
            @Text(label = "Render Mode", language = Language.English),
            @Text(label = "渲染模式", language = Language.Chinese)
    })
    private final ModeSetting renderMode = new ModeSetting("MC", Arrays.asList("MC", "Skia"),
            new SettingAttribute<>(uiStyle, "Skia"),
            new SettingAttribute<>(skiaXOffset, "Skia"),
            new SettingAttribute<>(skiaYOffset, "Skia"),
            new SettingAttribute<>(blurValue, "Skia")
    );

    private static CanvasStack cachedCanvasStack = null;
    private static long lastCanvasUpdate = 0;
    private static final long CANVAS_CACHE_TIME = 100;




    private static class RenderState {
        float x, y, width, height;
        String mainText, subText;
        float healthRate;
        LivingEntity entity;
        boolean isValid = false;

        void update(float x, float y, float width, float height, String mainText, String subText, float healthRate, LivingEntity entity) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.mainText = mainText;
            this.subText = subText;
            this.healthRate = healthRate;
            this.entity = entity;
            this.isValid = true;
        }

        void invalidate() {
            this.isValid = false;
            this.entity = null;
        }
    }

    private final RenderState currentRenderState = new RenderState();

    // 矩阵捕获系统（用于装备2D渲染）
    private static final Matrix4f projectionMatrix = new Matrix4f();
    private static final Matrix4f modelViewMatrix = new Matrix4f();
    private static final Matrix4f worldMatrix = new Matrix4f();

    public NameTags() {
        registerSetting(renderMode, healthValue, distanceValue, armorValue, scaleValue, self, disableVanilla);
    }

    @EventTarget(4)
    public void onRender3D(EventRender3D event) {
        if (mc.player == null)
            return;

        // 捕获矩阵用于装备2D投影
        captureWorldMatrix(event.getPoseStack());
        captureViewMatrices();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity) {
                if (entity == mc.player) {
                    if (mc.options.getCameraType().isFirstPerson()) continue;
                    if (!self.getValue()) {
                        continue;
                    }
                }

                if (!Utils.isValidEntity((LivingEntity) entity))
                    continue;

                renderNameTag((LivingEntity) entity, Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText()), event.getPoseStack());
            }
        }
    }

    @EventTarget(4)
    public void onRender2D(EventRender2D event) {
        if (mc.player == null || mc.level == null)
            return;

        // 只在Skia模式下渲染装备（Skia模式使用2D投影，MC模式使用3D渲染）
        if (armorValue.getValue() && renderMode.getValue().equals("Skia")) {
            for (Entity entity : mc.level.entitiesForRendering()) {
                if (entity instanceof LivingEntity && entity instanceof Player) {
                    if (entity == mc.player) {
                        if (mc.options.getCameraType().isFirstPerson()) continue;
                        if (!self.getValue()) {
                            continue;
                        }
                    }

                    if (!Utils.isValidEntity((LivingEntity) entity))
                        continue;

                    // 获取实体的屏幕位置
                    Vec3 screenPos = getEntityScreenPosition((LivingEntity) entity);
                    if (screenPos != null) {
                        renderEquipment2D(event, (Player) entity, screenPos);
                    }
                }
            }
        }
    }

    private final DecimalFormat dFormat = new DecimalFormat("0.0");







    private void renderNameTag(LivingEntity entity, String tag, PoseStack poseStack) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");
        boolean bot = AntiBot.isBot(entity);
        boolean killer = KillerCheck.isKiller(entity);
        boolean friend = false;
        boolean team = Targets.isTeam(entity) || (clayGuns.isEnable() && clayGuns.teamName.contains(entity.getName().getString()));
        float a = HeypixelManager.getEntityHealth(entity);

        String nameColor = bot ? "§3" : entity.isInvisible() ? "§6" : entity.isCrouching() ? "§4" : ChatFormatting.WHITE + "";
        String distanceText = distanceValue.getValue() ? " §7" + (int) mc.player.distanceTo(entity) + "m" : "";
        String healthDisplay = dFormat.format(a);
        String healthText = healthValue.getValue() ? "§7§cHP: " + healthDisplay : "";
        String botText = bot ? " §c§lBot " : "";
        String killerText = killer ? " §cKiller " : "";
        String friendText = friend ? " §c§lFriend " : "";
        String teamText = team ? ChatFormatting.AQUA + "[Team] " : "";
        String text = healthText + distanceText;

        String attitudeText = "";
        if (entity instanceof Player player) {
            boolean isGodAxe = PlayerUtils.isHoldingGodAxe(player);
            attitudeText = isGodAxe ? " §4§l[GodAxe]" : "";
        }
        String mainText = teamText + nameColor + tag + botText + killerText + friendText + attitudeText;
        poseStack.pushPose();
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();

        poseStack.translate(
                entity.xOld + (entity.getX() - entity.xOld) * mc.getFrameTime() - renderManager.camera.getPosition().x,
                entity.yOld + (entity.getY() - entity.yOld) * mc.getFrameTime() - renderManager.camera.getPosition().y + entity.getEyeHeight() + 0.55,
                entity.zOld + (entity.getZ() - entity.zOld) * mc.getFrameTime() - renderManager.camera.getPosition().z
        );

        poseStack.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
        poseStack.mulPose(Axis.XP.rotationDegrees(renderManager.camera.getXRot()));

        float distance = (float) (mc.player.distanceTo(entity) * 0.25);
        if (distance < 1F) distance = 1F;
        float scale = distance / 100f * scaleValue.getValue().floatValue();
        poseStack.scale(-scale, -scale, scale);
        float width = Math.max(FontRenderers.misans18.getStringWidth(text), FontRenderers.misans18.getStringWidth(mainText)) * 0.5f;
        float height = FontRenderers.misans18.getHeight() + 16;
        float rate = HeypixelManager.getHealthRate(entity);
        currentRenderState.update(-width - 2F, -(height), width * 2 + 4F, height, mainText, text, rate, entity);
        if (renderMode.getValue().equals("Skia")) {
            // 使用新的Skia3DRenderer工具类
            renderWithSkia3DRenderer(entity, poseStack);
        } else {
            renderWithVanilla(poseStack, mainText, text, entity);
        }

        currentRenderState.invalidate();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        poseStack.popPose();
    }





    /**
     * 使用新的Skia3DRenderer工具类进行渲染
     */
    private void renderWithSkia3DRenderer(LivingEntity entity, PoseStack poseStack) {
        if (!currentRenderState.isValid || currentRenderState.entity == null) return;

        // 配置渲染参数
        Skia3DRenderer.RenderConfig config = new Skia3DRenderer.RenderConfig()
                .setBaseScale(scaleValue.getValue().floatValue())
                .setYOffset(0.55f)
                .setEnableDistanceScaling(true)
                .setEnableBillboard(true);

        // 使用Skia3DRenderer进行渲染
        boolean success = Skia3DRenderer.render3D(entity, poseStack, config, context -> {
            // 在这个回调中进行实际的2D渲染
            CanvasStack canvasStack = context.canvasStack;
            Vec3 screenPos = context.screenPosition;

            // 根据UI风格选择渲染方法
            if (uiStyle.getValue().equals("Modern")) {
                renderModernSkiaUI(canvasStack, screenPos);
            } else {
                renderClassicSkiaUI(canvasStack, screenPos);
            }
        });

        // 如果Skia渲染失败，回退到透明渲染
        if (!success) {
            renderTransparentVanilla(poseStack);
        }
    }



    // 现代UI风格 - 分层显示，主文本和子文本分开
    private void renderModernSkiaUI(CanvasStack canvasStack, Vec3 screenPos) {
        canvasStack.push();
        
        // 获取带颜色的主文本
        String mainDisplayText = currentRenderState.mainText;
        
        // 构建子文本（血量、距离等）
        StringBuilder subTextBuilder = new StringBuilder();
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(currentRenderState.entity);
            subTextBuilder.append("§7§c").append(dFormat.format(health)).append("HP");
        }
        if (distanceValue.getValue()) {
            if (subTextBuilder.length() > 0) subTextBuilder.append(" ");
            int distance = (int) mc.player.distanceTo(currentRenderState.entity);
            subTextBuilder.append("§7").append(distance).append("m");
        }
        String subDisplayText = subTextBuilder.toString();

        // 计算文本尺寸（使用SkiaRender的方法）
        float baseMainTextWidth = SkiaRender.getCleanTextWidth(mainDisplayText, 14);
        float baseSubTextWidth = !subDisplayText.isEmpty() ? SkiaRender.getCleanTextWidth(subDisplayText, 12) : 0;
        float baseTextWidth = Math.max(baseMainTextWidth, baseSubTextWidth);
        float baseTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 1.5f;
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = skiaXOffset.getValue().floatValue();
        float yOffset = skiaYOffset.getValue().floatValue();

        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - textWidth / 2f;
        float textTop = textCenterY - textHeight / 2f;
        
        // 背景计算
        float padding = 1.4f;
        float totalHeight = baseTextHeight * manualScale;
        if (!subDisplayText.isEmpty()) {
            totalHeight += SkiaFontManager.getDefaultFont12().getActualHeight() * manualScale + 2f;
        }
        
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = textWidth + (padding * 2);
        float bgHeight = totalHeight + (padding * 2);
        float cornerRadius = 4f;
        
        // 渲染背景
        if (blurValue.getValue()) {
            float blurRadius = 8f;
            SkiaRender.drawBlurRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, blurRadius);
            int glassColor = new Color(240, 240, 240, 60).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, glassColor);
        } else {
            int backgroundColor = new Color(200, 200, 200, 120).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);
        }
        
        // 使用SkiaRender渲染主文本（带颜色，带缩放）
        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledMainX = textLeft / manualScale;
        float scaledMainY = textTop / manualScale;
        SkiaRender.drawColoredText(canvasStack, mainDisplayText, scaledMainX, scaledMainY, 14, false);
        canvasStack.pop();
        
        // 渲染子文本（血量、距离等，带颜色）
        if (!subDisplayText.isEmpty()) {
            float subTextY = textTop + (SkiaFontManager.getDefaultFont14().getActualHeight() * manualScale) + 2f;
            canvasStack.push();
            canvasStack.canvas.scale(manualScale * 0.85f, manualScale * 0.85f); // 子文本稍小一些
            float scaledSubX = textLeft / (manualScale * 0.85f);
            float scaledSubY = subTextY / (manualScale * 0.85f);
            SkiaRender.drawColoredText(canvasStack, subDisplayText, scaledSubX, scaledSubY, 12, false);
            canvasStack.pop();
        }

        // 渲染装备（如果启用）
        if (armorValue.getValue() && currentRenderState.entity instanceof Player) {
            renderModernArmorSkia(canvasStack, (Player) currentRenderState.entity, textCenterX, textTop, totalHeight, manualScale);
        }

        canvasStack.pop();
    }

    // 经典UI风格 - 传统的 player [20HP] [1m] 格式
    private void renderClassicSkiaUI(CanvasStack canvasStack, Vec3 screenPos) {
        canvasStack.push();
        
        // 构建经典格式的文本
        String playerName = Utils.getStringFromFormattedCharSequence(currentRenderState.entity.getDisplayName().getVisualOrderText());
        StringBuilder classicTextBuilder = new StringBuilder();
        classicTextBuilder.append(playerName);
        
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(currentRenderState.entity);
            classicTextBuilder.append(" §7[§c").append(dFormat.format(health)).append("HP§7]");
        }
        
        if (distanceValue.getValue()) {
            int distance = (int) mc.player.distanceTo(currentRenderState.entity);
            classicTextBuilder.append(" §7[§f").append(distance).append("m§7]");
        }
        
        String classicText = classicTextBuilder.toString();

        // 计算文本尺寸
        float baseTextWidth = SkiaRender.getCleanTextWidth(classicText, 14);
        float baseTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 1.5f;
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = skiaXOffset.getValue().floatValue();
        float yOffset = skiaYOffset.getValue().floatValue();

        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - textWidth / 2f;
        float textTop = textCenterY - textHeight / 2f;
        
        // 背景计算
        float padding = 1.4f;
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = textWidth + (padding * 2);
        float bgHeight = textHeight + (padding * 2);
        float cornerRadius = 4f;
        
        // 渲染背景
        if (blurValue.getValue()) {
            float blurRadius = 8f;
            SkiaRender.drawBlurRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, blurRadius);
            int glassColor = new Color(240, 240, 240, 60).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, glassColor);
        } else {
            int backgroundColor = new Color(200, 200, 200, 120).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);
        }
        
        // 渲染经典格式文本（带颜色，带缩放）
        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledX = textLeft / manualScale;
        float scaledY = textTop / manualScale;
        SkiaRender.drawColoredText(canvasStack, classicText, scaledX, scaledY, 14, false);
        canvasStack.pop();

        // 渲染装备（如果启用）
        if (armorValue.getValue() && currentRenderState.entity instanceof Player) {
            renderClassicArmorSkia(canvasStack, (Player) currentRenderState.entity, textCenterX, textTop, textHeight, manualScale);
        }

        canvasStack.pop();
    }

    private String buildDisplayText(LivingEntity entity, String entityName) {
        StringBuilder text = new StringBuilder();
        text.append(entityName);
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(entity);
            text.append(" [").append(dFormat.format(health)).append("HP]");
        }
        if (distanceValue.getValue()) {
            int distance = (int) mc.player.distanceTo(entity);
            text.append(" [").append(distance).append("m]");
        }

        return text.toString();
    }

    private void renderTransparentVanilla(PoseStack poseStack) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderUtils.drawRect(poseStack, currentRenderState.x, currentRenderState.y,
                currentRenderState.width, currentRenderState.height,
                new Color(0, 0, 0, 0).getRGB());
    }

    private CanvasStack getCachedSkiaCanvas() {
        long currentTime = System.currentTimeMillis();
        if (cachedCanvasStack != null && (currentTime - lastCanvasUpdate) < CANVAS_CACHE_TIME) {
            return cachedCanvasStack;
        }
        try {
            if (SkiaProcess.INSTANCE == null ||
                    SkiaProcess.INSTANCE.skiaUtils == null ||
                    SkiaProcess.INSTANCE.skiaUtils.surface == null ||
                    SkiaProcess.INSTANCE.skiaUtils.canvas == null) {
                cachedCanvasStack = null;
                return null;
            }

            cachedCanvasStack = new CanvasStack(SkiaProcess.INSTANCE.skiaUtils);
            lastCanvasUpdate = currentTime;
            return cachedCanvasStack;

        } catch (Exception e) {
            cachedCanvasStack = null;
            return null;
        }
    }

    private Vec3 getCorrectScreenPosition(LivingEntity entity) {
        try {
            if (Main.INSTANCE.projection == null) {
                return null;
            }

            EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
            Camera camera = renderManager.camera;
            float partialTicks = mc.getFrameTime();

            double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
            double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + 0.55;
            double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;

            Vec3 relativePos = new Vec3(
                    entityX - camera.getPosition().x,
                    entityY - camera.getPosition().y,
                    entityZ - camera.getPosition().z
            );

            return Main.INSTANCE.projection.projectToScreen(relativePos);

        } catch (Exception e) {
            return null;
        }
    }



    private void renderWithVanilla(PoseStack poseStack, String mainText, String text, LivingEntity entity) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();

        float mainTextWidth = FontRenderers.misans18.getStringWidth(mainText);
        float subTextWidth = FontRenderers.misans14.getStringWidth(text);
        float width = Math.max(mainTextWidth, subTextWidth) * 0.5f;
        float height = FontRenderers.misans18.getHeight();

        RenderUtils.drawRect(poseStack, -width - 2F, 0, width + 4F, -(height + 16),
                new Color(24, 24, 24, 160).getRGB());

        Color color = getHealthColor(currentRenderState.healthRate);
        float healthBarWidth = (width + 4F - (-width - 2F)) * currentRenderState.healthRate; // 原始计算方式
        RenderUtils.drawRect(poseStack, -width - 2F, -1.5f, healthBarWidth, 1.5f, color.getRGB());

        FontRenderers.misans18.drawString(poseStack, mainText, 1F - width, -20f, Color.WHITE.getRGB());
        FontRenderers.misans14.drawString(poseStack, text, 1F - width, -9F, Color.WHITE.getRGB());

        // 在MC模式下渲染装备（在同一个PoseStack变换中）
        if (armorValue.getValue() && entity instanceof Player) {
            renderArmorInVanilla(poseStack, entity, width);
        }
    }



    /**
     * 在Vanilla渲染中渲染装备（在文本上方）
     */
    private void renderArmorInVanilla(PoseStack poseStack, LivingEntity entity, float textWidth) {
        // 获取装备物品（按照正确的顺序：头盔、胸甲、护腿、靴子、主手、副手）
        java.util.List<ItemStack> armorItems = new java.util.ArrayList<>();

        if (entity instanceof Player player) {
            // 按顺序添加盔甲（头到脚）
            for (ItemStack armorItem : player.getArmorSlots()) {
                armorItems.add(0, armorItem); // 反向添加，因为getArmorSlots()是从脚到头
            }
            // 添加主手和副手
            armorItems.add(player.getMainHandItem());
            armorItems.add(player.getOffhandItem());
        }

        // 移除空物品
        armorItems.removeIf(ItemStack::isEmpty);

        if (armorItems.isEmpty()) return;

        // 计算装备显示位置（在文本上方）
        float itemSize = 12f; // 调整为更小的尺寸
        float itemSpacing = 1f; // 减小间距
        float totalWidth = armorItems.size() * itemSize + (armorItems.size() - 1) * itemSpacing;
        float startX = -totalWidth / 2f;
        float startY = -35f; // 在文本上方

        // 渲染每个装备
        for (int i = 0; i < armorItems.size(); i++) {
            ItemStack item = armorItems.get(i);
            if (!item.isEmpty()) {
                float itemX = startX + i * (itemSize + itemSpacing);
                float itemY = startY;

                // 简化装备渲染，使用与文本相同的方式
                RenderUtils.renderAndDecorateItem(poseStack, item, (int)itemX, (int)itemY);
            }
        }
    }

    private void renderArmor(PoseStack poseStack, LivingEntity entity) {
        int[] indices = new int[]{4, 5, 3, 2, 1, 0};

        for (int i = 0; i < indices.length; i++) {
            int index = indices[i];
            ItemStack equipmentInSlot = getEquipmentInSlot(index, entity);
            poseStack.pushPose();
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            RenderSystem.disableDepthTest();
            RenderSystem.setShader(GameRenderer::getPositionColorShader);
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);

            GL20.glEnable(32823);
            GL20.glPolygonOffset(1.0f, -1000000.0f);
            RenderUtils.renderAndDecorateItem(poseStack, equipmentInSlot, -57 + i * 20, -12);
            GL20.glPolygonOffset(1.0f, 1000000.0f);
            GL20.glDisable(32823);

            RenderSystem.enableDepthTest();
            RenderSystem.disableBlend();
            poseStack.popPose();
        }
    }


    private Color getHealthColor(float rate) {
        if (rate > 0.75) {
            return Color.GREEN;
        } else if (rate <= 0.75f && rate >= 0.5f) {
            return Color.ORANGE;
        } else if (rate <= 0.5f && rate >= 0.25f) {
            return new Color(255, 97, 24);
        } else {
            return Color.RED;
        }
    }

    public ItemStack getEquipmentInSlot(int index, LivingEntity entity) {
        EquipmentSlot equipmentSlot = EquipmentSlot.MAINHAND;
        switch (index) {
            case 0 -> equipmentSlot = EquipmentSlot.FEET;
            case 1 -> equipmentSlot = EquipmentSlot.LEGS;
            case 2 -> equipmentSlot = EquipmentSlot.CHEST;
            case 3 -> equipmentSlot = EquipmentSlot.HEAD;
            case 4 -> {
            }
            case 5 -> equipmentSlot = EquipmentSlot.OFFHAND;
        }

        return entity.getItemBySlot(equipmentSlot);
    }
    
    public boolean isDisableVanilla() {
        return disableVanilla.getValue();
    }

    /**
     * 渲染Modern风格的装备显示
     */
    private void renderModernArmorSkia(CanvasStack canvasStack, Player player, float centerX, float textTop, float textHeight, float scale) {
        // 获取装备物品（按照正确的顺序：头盔、胸甲、护腿、靴子、主手）
        java.util.List<ItemStack> armorItems = new java.util.ArrayList<>();

        // 按顺序添加盔甲（头到脚）
        for (ItemStack armorItem : player.getArmorSlots()) {
            armorItems.add(0, armorItem); // 反向添加，因为getArmorSlots()是从脚到头
        }
        // 添加主手和副手
        armorItems.add(player.getMainHandItem());
        armorItems.add(player.getOffhandItem());

        // 移除空物品
        armorItems.removeIf(ItemStack::isEmpty);

        if (armorItems.isEmpty()) return;

        // 计算装备显示位置（在文本上方）
        float itemSize = 16f * scale; // 装备图标大小
        float itemSpacing = 2f * scale; // 装备间距
        float totalWidth = armorItems.size() * itemSize + (armorItems.size() - 1) * itemSpacing;

        float armorStartX = centerX - totalWidth / 2f;
        float armorY = textTop - itemSize - 8f * scale; // 在文本上方显示

        // 绘制装备背景
        float bgPadding = 4f * scale;
        float bgX = armorStartX - bgPadding;
        float bgY = armorY - bgPadding;
        float bgWidth = totalWidth + bgPadding * 2;
        float bgHeight = itemSize + bgPadding * 2;

        int backgroundColor = new Color(20, 20, 20, 180).getRGB();
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, 4f, backgroundColor);

        // 渲染每个装备（使用GameFrameBuffer方式）
        renderArmorItemsWithFrameBuffer(canvasStack, armorItems, armorStartX, armorY, itemSize, itemSpacing);
    }

    /**
     * 渲染Classic风格的装备显示
     */
    private void renderClassicArmorSkia(CanvasStack canvasStack, Player player, float centerX, float textTop, float textHeight, float scale) {
        // 获取装备物品（按照正确的顺序：头盔、胸甲、护腿、靴子、主手）
        java.util.List<ItemStack> armorItems = new java.util.ArrayList<>();

        // 按顺序添加盔甲（头到脚）
        for (ItemStack armorItem : player.getArmorSlots()) {
            armorItems.add(0, armorItem); // 反向添加，因为getArmorSlots()是从脚到头
        }
        // 添加主手和副手
        armorItems.add(player.getMainHandItem());
        armorItems.add(player.getOffhandItem());

        // 移除空物品
        armorItems.removeIf(ItemStack::isEmpty);

        if (armorItems.isEmpty()) return;

        // 计算装备显示位置（在文本上方）
        float itemSize = 16f * scale; // 装备图标大小
        float itemSpacing = 2f * scale; // 装备间距
        float totalWidth = armorItems.size() * itemSize + (armorItems.size() - 1) * itemSpacing;

        float armorStartX = centerX - totalWidth / 2f;
        float armorY = textTop - itemSize - 6f * scale; // 在文本上方显示

        // 绘制装备背景
        float bgPadding = 4f * scale;
        float bgX = armorStartX - bgPadding;
        float bgY = armorY - bgPadding;
        float bgWidth = totalWidth + bgPadding * 2;
        float bgHeight = itemSize + bgPadding * 2;

        int backgroundColor = new Color(20, 20, 20, 180).getRGB();
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, 4f, backgroundColor);

        // 渲染每个装备（使用GameFrameBuffer方式）
        renderArmorItemsWithFrameBuffer(canvasStack, armorItems, armorStartX, armorY, itemSize, itemSpacing);
    }

    /**
     * 使用GameFrameBuffer渲染装备物品
     */
    private void renderArmorItemsWithFrameBuffer(CanvasStack canvasStack, java.util.List<ItemStack> armorItems,
                                               float startX, float startY, float itemSize, float itemSpacing) {
        try {
            // 创建一个专门用于装备渲染的GameFrameBuffer
            GameFrameBuffer armorFrameBuffer = FrameBuffers.getBuffer("NameTags_Armor");
            if (armorFrameBuffer == null) {
                System.err.println("Failed to get NameTags_Armor GameFrameBuffer");
                return;
            }

            armorFrameBuffer.execute(eventRender2D -> {
                PoseStack poseStack = eventRender2D.getPoseStack();
                GuiGraphics guiGraphics = eventRender2D.getGuiGraphics();

                poseStack.pushPose();
                RenderSystem.enableDepthTest();
                RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
                Lighting.setupFor3DItems();

                // 渲染每个装备物品
                for (int i = 0; i < armorItems.size(); i++) {
                    ItemStack item = armorItems.get(i);
                    if (!item.isEmpty()) {
                        float itemX = i * (itemSize + itemSpacing);
                        float itemY = 0;

                        // 使用GuiGraphics渲染物品
                        guiGraphics.renderItem(item, (int)itemX, (int)itemY);
                        guiGraphics.renderItemDecorations(mc.font, item, (int)itemX, (int)itemY);
                    }
                }

                poseStack.popPose();
            });

            // 将渲染结果绘制到Skia画布上
            canvasStack.push();
            canvasStack.canvas.translate(startX, startY);
            armorFrameBuffer.draw(canvasStack);
            canvasStack.pop();

        } catch (Exception e) {
            System.err.println("Error rendering armor items: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 捕获世界矩阵
     */
    private void captureWorldMatrix(PoseStack poseStack) {
        worldMatrix.set(poseStack.last().pose());
    }

    /**
     * 捕获视图矩阵
     */
    private void captureViewMatrices() {
        modelViewMatrix.set(RenderSystem.getModelViewMatrix());
        projectionMatrix.set(RenderSystem.getProjectionMatrix());
    }

    /**
     * 获取实体的屏幕位置（2D投影）
     */
    private Vec3 getEntityScreenPosition(LivingEntity entity) {
        try {
            float partialTicks = mc.getFrameTime();
            double x = Mth.lerp(partialTicks, entity.xOld, entity.getX());
            double y = Mth.lerp(partialTicks, entity.yOld, entity.getY()) + entity.getBbHeight() + 0.5;
            double z = Mth.lerp(partialTicks, entity.zOld, entity.getZ());

            Vector3f screenPos = projectOn2D((float) x, (float) y, (float) z, (float) mc.getWindow().getGuiScale());
            if (screenPos != null) {
                return new Vec3(screenPos.x, screenPos.y, screenPos.z);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将3D坐标投影到2D屏幕坐标
     */
    private Vector3f projectOn2D(float x, float y, float z, float scaleFactor) {
        Camera camera = mc.gameRenderer.getMainCamera();
        int displayHeight = mc.getWindow().getHeight();

        Vec3 cameraPos = camera.getPosition();
        double deltaX = x - cameraPos.x;
        double deltaY = y - cameraPos.y;
        double deltaZ = z - cameraPos.z;

        Vector4f transformed = new Vector4f((float) deltaX, (float) deltaY, (float) deltaZ, 1.0f).mul(worldMatrix);

        Matrix4f matrix = new Matrix4f(projectionMatrix);
        matrix.mul(modelViewMatrix);

        Vector3f screenCoords = new Vector3f();
        matrix.project(transformed.x(), transformed.y(), transformed.z(),
                new int[]{0, 0, mc.getWindow().getWidth(), mc.getWindow().getHeight()},
                screenCoords);

        if (screenCoords.z > 1.0f) {
            return null;
        }

        return new Vector3f(
                screenCoords.x / scaleFactor,
                (displayHeight - screenCoords.y) / scaleFactor,
                screenCoords.z
        );
    }

    /**
     * 在2D空间中渲染NameTag
     */
    private void renderNameTag2D(EventRender2D event, LivingEntity entity, Vec3 screenPos) {
        if (screenPos == null || screenPos.z < 0.0 || screenPos.z >= 1.0) return;

        PoseStack poseStack = event.getPoseStack();
        GuiGraphics guiGraphics = event.getGuiGraphics();

        poseStack.pushPose();
        poseStack.translate(screenPos.x, screenPos.y, 0);

        // 距离自适应缩放（调整为更小的尺寸）
        float distance = mc.player.distanceTo(entity);
        float scale = Math.max(0.3F, Math.min(0.6F, 5.0F / distance)) * scaleValue.getValue().floatValue();
        poseStack.scale(scale, scale, 1.0F);

        // 构建显示文本
        String mainText = Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText());
        String subText = buildSubText(entity);

        if (renderMode.getValue().equals("Skia")) {
            // 设置currentRenderState用于Skia渲染
            float healthRate = HeypixelManager.getHealthRate(entity);
            currentRenderState.update((float)screenPos.x, (float)screenPos.y, 0, 0, mainText, subText, healthRate, entity);
            renderWithSkiaInterception();
            currentRenderState.invalidate();
        } else {
            renderNameTag2DMC(event, entity, mainText, subText);
        }

        poseStack.popPose();
    }

    /**
     * 使用MC渲染在2D空间中渲染NameTag
     */
    private void renderNameTag2DMC(EventRender2D event, LivingEntity entity, String mainText, String subText) {
        PoseStack poseStack = event.getPoseStack();
        GuiGraphics guiGraphics = event.getGuiGraphics();

        float mainTextWidth = FontRenderers.misans18.getStringWidth(mainText);
        float subTextWidth = FontRenderers.misans14.getStringWidth(subText);
        float width = Math.max(mainTextWidth, subTextWidth) * 0.5f;
        float height = FontRenderers.misans18.getHeight();

        // 渲染装备（在文本上方）
        if (armorValue.getValue() && entity instanceof Player) {
            renderEquipment2D(event, (Player) entity, -height - 35);
        }

        // 渲染背景
        RenderUtils.drawRect(poseStack, -width - 2F, 0, width + 4F, -(height + 16),
                new Color(24, 24, 24, 160).getRGB());

        // 渲染血量条
        float healthRate = HeypixelManager.getHealthRate(entity);
        Color color = getHealthColor(healthRate);
        float healthBarWidth = (width + 4F - (-width - 2F)) * healthRate;
        RenderUtils.drawRect(poseStack, -width - 2F, -1.5f, healthBarWidth, 1.5f, color.getRGB());

        // 渲染文本
        FontRenderers.misans18.drawString(poseStack, mainText, 1F - width, -20f, Color.WHITE.getRGB());
        FontRenderers.misans14.drawString(poseStack, subText, 1F - width, -9F, Color.WHITE.getRGB());
    }

    /**
     * 使用Skia渲染（采用老版本的成功方式）
     */
    private void renderWithSkiaInterception() {
        if (!currentRenderState.isValid || currentRenderState.entity == null) return;

        CanvasStack canvasStack = getCachedSkiaCanvas();
        if (canvasStack == null) {
            return;
        }

        Vec3 screenPos = getCorrectScreenPosition(currentRenderState.entity);
        if (screenPos == null) {
            return;
        }

        canvasStack.push();
        String displayText = buildDisplayText(currentRenderState.entity, currentRenderState.mainText);

        float baseTextWidth = SkiaFontManager.getDefaultFont12().getWidth(displayText);
        float baseTextHeight = SkiaFontManager.getDefaultFont12().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 3.0f; // 调整缩放
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = 0f; // 不使用额外偏移
        float yOffset = 0f;

        float actualTextWidth = SkiaFontManager.getDefaultFont14().getWidth(displayText) * manualScale;
        float actualTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight() * manualScale;
        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - actualTextWidth / 2f;
        float textTop = textCenterY - actualTextHeight / 2f;

        // 渲染装备（在文本上方）
        if (armorValue.getValue() && currentRenderState.entity instanceof Player) {
            renderSkiaEquipment(canvasStack, (Player) currentRenderState.entity, textCenterX, textTop - 20, manualScale);
        }

        float padding = 1.4f;
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = actualTextWidth + (padding * 2);
        float bgHeight = actualTextHeight + (padding * 2);
        float cornerRadius = 4f;

        int backgroundColor = new Color(24, 24, 24, 160).getRGB();
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);

        // 渲染血量条
        float healthRate = HeypixelManager.getHealthRate(currentRenderState.entity);
        Color healthColor = getHealthColor(healthRate);
        float healthBarWidth = bgWidth * healthRate;
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY + bgHeight - 2, healthBarWidth, 2f, 1f, healthColor.getRGB());

        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledTextX = textLeft / manualScale;
        float scaledTextY = textTop / manualScale;

        SkiaFontManager.getDefaultFont14().drawText(canvasStack, displayText,
                scaledTextX, scaledTextY, Color.WHITE.getRGB());

        canvasStack.pop();
        canvasStack.pop();
    }





    /**
     * 在2D空间中渲染装备
     */
    private void renderEquipment2D(EventRender2D event, Player player, float yOffset) {
        java.util.List<ItemStack> equipment = new java.util.ArrayList<>();

        // 按顺序添加盔甲（头到脚）
        for (ItemStack armorItem : player.getArmorSlots()) {
            equipment.add(0, armorItem); // 反向添加
        }
        // 添加主手和副手
        equipment.add(player.getMainHandItem());
        equipment.add(player.getOffhandItem());

        // 移除空物品
        equipment.removeIf(ItemStack::isEmpty);

        if (equipment.isEmpty()) return;

        GuiGraphics graphics = event.getGuiGraphics();
        PoseStack poseStack = graphics.pose();

        // 调整装备大小和间距
        float itemSize = 12f; // 减小装备图标大小
        float itemSpacing = 1f; // 减小间距
        float boxWidth = equipment.size() * itemSize + (equipment.size() + 1) * itemSpacing;
        float boxX = -boxWidth / 2;
        float boxHeight = itemSize + 4;

        // 渲染装备背景
        RenderUtils.drawRect(poseStack, boxX, yOffset, boxX + boxWidth, yOffset + boxHeight,
                new Color(20, 20, 20, 180).getRGB());

        // 渲染每个装备
        for (int i = 0; i < equipment.size(); i++) {
            ItemStack stack = equipment.get(i);
            if (!stack.isEmpty()) {
                float itemX = boxX + itemSpacing + i * (itemSize + itemSpacing);
                float itemY = yOffset + 2;

                // 缩放装备渲染
                poseStack.pushPose();
                poseStack.translate(itemX, itemY, 0);
                poseStack.scale(itemSize / 16f, itemSize / 16f, 1f);
                graphics.renderItem(stack, 0, 0);
                graphics.renderItemDecorations(mc.font, stack, 0, 0);
                poseStack.popPose();
            }
        }
    }

    /**
     * 构建子文本（血量、距离等）
     */
    private String buildSubText(LivingEntity entity) {
        StringBuilder text = new StringBuilder();

        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(entity);
            text.append("§c").append(dFormat.format(health)).append("HP");
        }

        if (distanceValue.getValue()) {
            if (text.length() > 0) text.append(" ");
            int distance = (int) mc.player.distanceTo(entity);
            text.append("§7[").append(distance).append("m]");
        }

        return text.toString();
    }

    /**
     * 在Skia中渲染装备
     */
    private void renderSkiaEquipment(CanvasStack canvasStack, Player player, float centerX, float centerY, float scale) {
        // 获取装备物品（按照正确的顺序：头盔、胸甲、护腿、靴子、主手、副手）
        java.util.List<ItemStack> equipment = new java.util.ArrayList<>();

        // 按顺序添加盔甲（头到脚）
        for (ItemStack armorItem : player.getArmorSlots()) {
            equipment.add(0, armorItem); // 反向添加
        }
        // 添加主手和副手
        equipment.add(player.getMainHandItem());
        equipment.add(player.getOffhandItem());

        // 移除空物品
        equipment.removeIf(ItemStack::isEmpty);

        if (equipment.isEmpty()) return;

        // 计算装备显示位置
        float itemSize = 12f * scale; // 调整为更小的尺寸
        float itemSpacing = 1f * scale;
        float totalWidth = equipment.size() * itemSize + (equipment.size() - 1) * itemSpacing;

        float startX = centerX - totalWidth / 2f;
        float startY = centerY;

        // 绘制装备背景
        float bgPadding = 2f * scale;
        float bgX = startX - bgPadding;
        float bgY = startY - bgPadding;
        float bgWidth = totalWidth + bgPadding * 2;
        float bgHeight = itemSize + bgPadding * 2;

        int backgroundColor = new Color(20, 20, 20, 180).getRGB();
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, 4f, backgroundColor);

        // 渲染每个装备（使用GameFrameBuffer方式）
        renderArmorItemsWithFrameBuffer(canvasStack, equipment, startX, startY, itemSize, itemSpacing);
    }



    /**
     * 在Skia3D中渲染装备
     */
    private void renderSkia3DEquipment(CanvasStack canvasStack, Player player) {
        // 获取装备物品
        java.util.List<ItemStack> equipment = new java.util.ArrayList<>();
        for (ItemStack armorItem : player.getArmorSlots()) {
            equipment.add(0, armorItem);
        }
        equipment.add(player.getMainHandItem());
        equipment.add(player.getOffhandItem());
        equipment.removeIf(ItemStack::isEmpty);

        if (equipment.isEmpty()) return;

        // 计算装备显示位置（在文本上方，使用相对坐标）
        float itemSize = 12f;
        float itemSpacing = 1f;
        float totalWidth = equipment.size() * itemSize + (equipment.size() - 1) * itemSpacing;

        // 使用相对坐标，就像MC渲染一样
        float startX = -totalWidth / 2f;
        float startY = -itemSize - 20f; // 在文本上方

        // 绘制装备背景
        float bgPadding = 2f;
        float bgX = startX - bgPadding;
        float bgY = startY - bgPadding;
        float bgWidth = totalWidth + bgPadding * 2;
        float bgHeight = itemSize + bgPadding * 2;

        int backgroundColor = new Color(20, 20, 20, 180).getRGB();
        SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, 4f, backgroundColor);

        // 渲染每个装备
        renderArmorItemsWithFrameBuffer(canvasStack, equipment, startX, startY, itemSize, itemSpacing);
    }



    /**
     * 在2D空间中渲染装备
     */
    private void renderEquipment2D(EventRender2D event, Player player, Vec3 screenPos) {
        // 获取装备（正确的顺序）
        java.util.List<ItemStack> equipment = new java.util.ArrayList<>();
        for (ItemStack armorItem : player.getArmorSlots()) {
            equipment.add(0, armorItem); // 反向添加：头盔→胸甲→护腿→靴子
        }
        equipment.add(player.getMainHandItem());
        equipment.add(player.getOffhandItem());
        equipment.removeIf(ItemStack::isEmpty);

        if (equipment.isEmpty()) return;

        PoseStack poseStack = event.getPoseStack();
        poseStack.pushPose();
        poseStack.translate(screenPos.x, screenPos.y, 0); // 移动到正确的屏幕位置

        // 距离自适应缩放
        float distance = mc.player.distanceTo(player);
        float scale = Math.max(0.3F, Math.min(0.6F, 5.0F / distance)) * scaleValue.getValue().floatValue();
        poseStack.scale(scale, scale, 1.0F);

        // 计算装备布局
        float itemSize = 12f;
        float itemSpacing = 1f;
        float totalWidth = equipment.size() * itemSize + (equipment.size() - 1) * itemSpacing;
        float startX = -totalWidth / 2f;
        float startY = -35f; // 在文本上方

        // 渲染装备背景
        float bgPadding = 2f;
        float bgX = startX - bgPadding;
        float bgY = startY - bgPadding;
        float bgWidth = totalWidth + bgPadding * 2;
        float bgHeight = itemSize + bgPadding * 2;

        RenderUtils.drawRect(poseStack, bgX, bgY, bgX + bgWidth, bgY + bgHeight,
                new Color(20, 20, 20, 180).getRGB());

        // 在2D空间中渲染装备
        GuiGraphics graphics = event.getGuiGraphics();
        for (int i = 0; i < equipment.size(); i++) {
            ItemStack stack = equipment.get(i);
            if (!stack.isEmpty()) {
                float itemX = startX + i * (itemSize + itemSpacing);
                float itemY = startY;
                graphics.renderItem(stack, (int) itemX, (int) itemY);
                graphics.renderItemDecorations(mc.font, stack, (int) itemX, (int) itemY);
            }
        }

        poseStack.popPose();
    }
}

