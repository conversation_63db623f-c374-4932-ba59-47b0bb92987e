package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.module.modules.other.NameProtect;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.Utils;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.scores.*;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ScoreboardHud extends AbsHudElement {
    private static final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");

    @SettingInfo(name = { @Text(label = "Background", language = Language.English), @Text(label = "显示背景", language = Language.Chinese) })
    public static final BooleanSetting background = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "ShowNumbers", language = Language.English), @Text(label = "显示数字", language = Language.Chinese) })
    public static final BooleanSetting showNumbers = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "Delete", language = Language.English), @Text(label = "删除", language = Language.Chinese) })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };

    public ScoreboardHud() {
        super("Scoreboard", 0.85, 0.3, 150, 200);
        registerSetting(background, showNumbers, delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        if (mc.player == null || mc.level == null) return;
        Scoreboard scoreboard = mc.level.getScoreboard();
        Objective objective = scoreboard.getDisplayObjective(1);
        if (objective == null) return;
        List<Score> scoreList = scoreboard.getPlayerScores(objective).stream()
                .filter(score -> !score.getOwner().startsWith("#"))
                .collect(Collectors.toList());

        if (scoreList.size() > 15) {
            scoreList = scoreList.subList(scoreList.size() - 15, scoreList.size());
        }
        if (scoreList.isEmpty()) return;
        String titleText = Utils.getStringFromFormattedCharSequence(objective.getDisplayName().getVisualOrderText());
        float textWidth = getCleanTextWidth(titleText);

        List<ScoreboardEntry> displayList = new ArrayList<>();
        for (Score score : scoreList) {
            String coloredName = Utils.getStringFromFormattedCharSequence(PlayerTeam.formatNameForTeam(scoreboard.getPlayersTeam(score.getOwner()), Component.literal(score.getOwner())).getVisualOrderText());
            NameProtect nameProtect = (NameProtect) Main.INSTANCE.moduleManager.getModule("NameProtect");
            if (nameProtect.isEnable() && mc.player != null && coloredName.contains(mc.player.getName().getString())) {
                coloredName = coloredName.replace(mc.player.getName().getString(), "Hide");
            }

            displayList.add(new ScoreboardEntry(score, coloredName));
            textWidth = Math.max(textWidth, getCleanTextWidth(showNumbers.getValue() ? (coloredName + " " + score.getScore()) : coloredName));
        }
        float width = textWidth + 10;
        float height = 18 + (displayList.size() * 8);
        setWidth(width);
        setHeight(height);

        if (ElementsHud.shadow.getValue()) {
            canvasStack.push();
            SkiaRender.scissorRoundedRect(canvasStack, 0, 0, width, height, 16, ClipMode.DIFFERENCE);
            SkiaRender.drawRoundedRectWithShadow(canvasStack, 0, 0, width, height, 16);
            canvasStack.pop();
        }

        if (background.getValue()) {
            if (ElementsHud.blur.getValue()) {
                SkiaRender.drawBlurRect(canvasStack, 0, 0, width, height, 16, 12);
            }
            SkiaRender.drawRoundedRect(canvasStack, 0, 0, width, height, 16, new Color(24, 24, 24, 120).getRGB());
        }

        float y = 5;
        drawScoreboardColoredText(canvasStack, titleText, 5, ElementsHud.fontShadow.getValue(),true);
        y += 8;

        for (int i = displayList.size() - 1; i >= 0; i--) {
            ScoreboardEntry entry = displayList.get(i);
            String text = entry.coloredName();
            drawScoreboardColoredText(canvasStack, text, y, ElementsHud.fontShadow.getValue(),false);

            if (showNumbers.getValue()) {
                String score = String.valueOf(entry.score().getScore());
                // 检查分数文本是否包含图标
                if (IconFont.containsIcons(score)) {
                    IconFont.drawTextWithIcons(canvasStack, score, width - 5 - getCleanTextWidth(score), y, new Color(255, 85, 85).getRGB(), ElementsHud.fontShadow.getValue());
                } else {
                    SkiaFontManager.getDefaultFont14().drawText(canvasStack, score, width - 5 - getCleanTextWidth(score), y, new Color(255, 85, 85).getRGB(), ElementsHud.fontShadow.getValue());
                }
            }
            y += 8;
        }
    }

    private void drawScoreboardColoredText(CanvasStack canvasStack, String text, float y, boolean shadow, boolean center) {
        if (text == null || text.isEmpty()) return;
        
        // 检查文本是否包含图标
        if (IconFont.containsIcons(text)) {
            float x = center ? (float) ((getWidth() - getCleanTextWidth(text)) / 2) : 5;
            IconFont.drawTextWithIcons(canvasStack, text, x, y, Color.WHITE.getRGB(), shadow);
            return;
        }
        
        float currentX = center ? (float) ((getWidth() - getCleanTextWidth(text)) / 2) : 5;
        int currentColor = Color.WHITE.getRGB();
        StringBuilder currentSegment = new StringBuilder();
        
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '§' && i + 1 < text.length()) {
                // 渲染当前段落
                if (!currentSegment.isEmpty()) {
                    SkiaFontManager.getDefaultFont14().drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
                    currentX += SkiaFontManager.getDefaultFont14().getWidth(currentSegment.toString());
                    currentSegment.setLength(0);
                }
                
                char colorChar = text.charAt(i + 1);
                
                // 处理十六进制颜色代码
                if (colorChar == 'x' && i + 13 < text.length()) {
                    try {
                        StringBuilder hexColor = new StringBuilder();
                        for (int j = 2; j < 14; j += 2) {
                            if (i + j + 1 < text.length() && text.charAt(i + j) == '§') {
                                hexColor.append(text.charAt(i + j + 1));
                            }
                        }
                        if (hexColor.length() == 6) {
                            currentColor = 0xFF000000 | Integer.parseInt(hexColor.toString(), 16);
                            i += 13;
                            continue;
                        }
                    } catch (NumberFormatException ignored) {
                        // 如果解析失败，继续处理普通颜色代码
                    }
                }
                
                // 处理标准颜色代码
                ChatFormatting format = ChatFormatting.getByCode(colorChar);
                if (format != null) {
                    if (format.isColor()) {
                        Integer color = format.getColor();
                        if (color != null) {
                            currentColor = color | 0xFF000000;
                        }
                    } else if (format == ChatFormatting.RESET) {
                        currentColor = Color.WHITE.getRGB();
                    }
                }
                i++; // 跳过颜色字符
                continue;
            }
            
            currentSegment.append(c);
        }
        
        // 渲染最后一段
        if (!currentSegment.isEmpty()) {
            SkiaFontManager.getDefaultFont14().drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
        }
    }

    private float getCleanTextWidth(String text) {
        return SkiaFontManager.getDefaultFont14().getWidth(COLOR_PATTERN.matcher(text).replaceAll(""));
    }

    private record ScoreboardEntry(Score score, String coloredName) {}
}