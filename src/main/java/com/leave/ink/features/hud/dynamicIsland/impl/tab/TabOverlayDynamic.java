package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import com.leave.ink.Main;
import com.leave.ink.features.hud.dynamicIsland.DynamicElement;
import com.leave.ink.features.module.modules.other.NameProtect;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.Utils;
import lombok.Setter;
import net.minecraft.ChatFormatting;
import net.minecraft.Optionull;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.client.multiplayer.ServerData;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.GameType;
import net.minecraft.world.scores.PlayerTeam;

import java.awt.*;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import net.minecraft.world.entity.player.Player;

@Setter
public class TabOverlayDynamic extends DynamicElement {
    private static final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");
    public static float listHeight = 0, listWidth = 0;
    private boolean keepAlive = false;

    private double cachedShiftWidth = 0;
    private double cachedShiftHeight = 0;
    private boolean needRecalculate = true;
    private long lastCalculationTime = 0;

    private int lastPlayerCount = 0;
    private int lastWindowWidth = 0;
    private int lastWindowHeight = 0;
    private String lastServerInfo = "";

    private double targetShiftWidth = 0;
    private double targetShiftHeight = 0;
    private double transitionSpeed = 0.15;

    public TabOverlayDynamic(String left, String right) {
        super(left, right,10);
        this.sticky = true;
    }

    @Override
    public void startFadeIn() {
        super.startFadeIn();
        needRecalculate = true;
        if (cachedShiftWidth == 0 && cachedShiftHeight == 0) {
            recalculateSize();
            cachedShiftWidth = targetShiftWidth;
            cachedShiftHeight = targetShiftHeight;
        }
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
        needRecalculate = true;
    }

    @Override
    public void resetInternalState() {
        cachedShiftWidth = 0;
        cachedShiftHeight = 0;
        targetShiftWidth = 0;
        targetShiftHeight = 0;
        needRecalculate = true;
        lastCalculationTime = 0;
        lastPlayerCount = 0;
        lastWindowWidth = 0;
        lastWindowHeight = 0;
        lastServerInfo = "";
    }

    private static final Comparator<PlayerInfo> PLAYER_COMPARATOR =
            Comparator.<PlayerInfo>comparingInt((p_253306_)
                    -> p_253306_.getGameMode() == GameType.SPECTATOR ? 1 : 0).thenComparing((p_269613_)
                    -> Optionull.mapOrDefault(p_269613_.getTeam(), PlayerTeam::getName, "")).thenComparing((p_253305_)
                    -> p_253305_.getProfile().getName(), String::compareToIgnoreCase);

    private List<PlayerInfo> getPlayerInfos() {
        return mc.player.connection.getListedOnlinePlayers().stream().sorted(PLAYER_COMPARATOR).toList();
    }

    private String truncateText(String text, int maxLength) {
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    private int playersPerColumn() {
        int windowHeight = mc.getWindow().getGuiScaledHeight();
        int windowWidth = mc.getWindow().getGuiScaledWidth();

        int widthBonus = 0;
        if (windowWidth >= 1920) {
            widthBonus = 4;
        } else if (windowWidth >= 1600) {
            widthBonus = 3;
        } else if (windowWidth >= 1366) {
            widthBonus = 2;
        }

        return Math.min(Math.min(Math.max(4, (windowHeight / 2 - 60) / 16), 8 + widthBonus), Math.max(4, (windowHeight / 2 - 60) / 16));
    }

    private int maxColumns() {
        int windowWidth = mc.getWindow().getGuiScaledWidth();
        List<PlayerInfo> players = getPlayerInfos();

        if (players.isEmpty()) return 1;

        float maxColumnWidth = 0;
        for (PlayerInfo playerInfo : players) {
            String displayName = playerInfo.getProfile().getName();
            maxColumnWidth = Math.max(maxColumnWidth, 16 + SkiaFontManager.getDefaultFont14().getWidth(displayName + " " + playerInfo.getLatency() + "ms"));
        }

        float availableWidth = windowWidth - 160;
        int maxPossibleColumns = (int) Math.floor(availableWidth / (maxColumnWidth + 6));

        return Math.max(1, Math.min(maxPossibleColumns, 8));
    }

    @Override
    public long getRetentionTime() {
        return keepAlive ? Long.MAX_VALUE : 0;
    }

    @Override
    public double getShiftHeight() {
        if (needRecalculate || hasKeyFactorsChanged()) {
            recalculateSize();
        }
        if (Math.abs(cachedShiftHeight - targetShiftHeight) > 0.5) {
            cachedShiftHeight += (targetShiftHeight - cachedShiftHeight) * transitionSpeed;
        } else {
            cachedShiftHeight = targetShiftHeight;
        }

        return cachedShiftHeight;
    }

    @Override
    public double getShiftWidth() {
        if (needRecalculate || hasKeyFactorsChanged()) {
            recalculateSize();
        }
        if (Math.abs(cachedShiftWidth - targetShiftWidth) > 0.5) {
            cachedShiftWidth += (targetShiftWidth - cachedShiftWidth) * transitionSpeed;
        } else {
            cachedShiftWidth = targetShiftWidth;
        }

        return cachedShiftWidth;
    }
    private boolean hasKeyFactorsChanged() {
        List<PlayerInfo> players = getPlayerInfos();
        int currentPlayerCount = players.size();
        int currentWindowWidth = mc.getWindow().getGuiScaledWidth();
        int currentWindowHeight = mc.getWindow().getGuiScaledHeight();
        String currentServerInfo = getColoredServerInfo();

        boolean changed = currentPlayerCount != lastPlayerCount ||
                currentWindowWidth != lastWindowWidth ||
                currentWindowHeight != lastWindowHeight ||
                !currentServerInfo.equals(lastServerInfo);

        if (changed) {
            lastPlayerCount = currentPlayerCount;
            lastWindowWidth = currentWindowWidth;
            lastWindowHeight = currentWindowHeight;
            lastServerInfo = currentServerInfo;
            return true;
        }
        return System.currentTimeMillis() - lastCalculationTime > 1000;
    }
    private void recalculateSize() {
        List<PlayerInfo> players = getPlayerInfos();
        if (players.isEmpty()) {
            targetShiftHeight = 60;
        } else {
            int playersPerColumn = playersPerColumn();
            int maxColumns = maxColumns();
            int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));
            int actualPlayersPerColumn = (int) Math.ceil(players.size() / (double) columns);
            int rowsNeeded = Math.min(actualPlayersPerColumn, players.size());
            targetShiftHeight = Math.max(60, rowsNeeded * 16 + 54);
        }
        if (players.isEmpty()) {
            targetShiftWidth = -24;
        } else {
            int playersPerColumn = playersPerColumn();
            int maxColumns = maxColumns();
            int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));

            float maxColumnWidth = 0;
            for (PlayerInfo playerInfo : players) {
                String displayName = getDisplayName(playerInfo);
                // 使用getCleanTextWidth方法计算宽度
                maxColumnWidth = Math.max(maxColumnWidth, 16 + getCleanTextWidth(displayName + " " + playerInfo.getLatency() + "ms"));
            }

            float totalWidth;
            if (columns == 1) {
                totalWidth = maxColumnWidth;
            } else {
                totalWidth = columns * maxColumnWidth + (columns - 1) * 6;
            }

            String serverInfo = getColoredServerInfo();
            String serverMOTD = getServerMOTD();
            String playerCount = getColoredPlayerCount();

            float serverInfoWidth = getCleanTextWidth(serverInfo, 16);
            float motdWidth = !serverMOTD.isEmpty() ? getCleanTextWidth(truncateText(serverMOTD, 50), 12) : 0;
            float playerCountWidth = getCleanTextWidth(playerCount, 14);

            totalWidth = Math.max(totalWidth, serverInfoWidth);
            totalWidth = Math.max(totalWidth, motdWidth);
            totalWidth = Math.max(totalWidth, playerCountWidth);

            int windowWidth = mc.getWindow().getGuiScaledWidth();
            float maxAllowedWidth = windowWidth - 160;
            totalWidth = Math.min(totalWidth, maxAllowedWidth);

            targetShiftWidth = (totalWidth - 130) / 2;
        }
        needRecalculate = false;
        lastCalculationTime = System.currentTimeMillis();
    }

    // 添加新方法：根据PlayerInfo查找对应的Player实体
    private Player findPlayerByUUID(PlayerInfo playerInfo) {
        if (mc.level == null || playerInfo == null) return null;
        
        try {
            String targetName = playerInfo.getProfile().getName();
            // 遍历世界中的所有玩家，查找匹配的玩家
            for (Player player : mc.level.players()) {
                if (player.getName().getString().equals(targetName) && 
                    player.getUUID().equals(playerInfo.getProfile().getId())) {
                    return player;
                }
            }
        } catch (Exception e) {
            // 如果出现异常，返回null，使用后备方案
        }
        
        return null;
    }

    private String getDisplayName(PlayerInfo playerInfo) {
        // 首先尝试根据UUID查找对应的Player实体
        Player player = findPlayerByUUID(playerInfo);
        String coloredName;
        
        if (player != null) {
            // 如果找到了对应的Player实体，使用实体的显示名称（就像NameTags一样）
            // 这样能获取到服务器设置的完整颜色信息
            coloredName = Utils.getStringFromFormattedCharSequence(player.getDisplayName().getVisualOrderText());
        } else {
            // 如果没有找到实体，使用原来的方法作为后备方案
            Component tabDisplayName = playerInfo.getTabListDisplayName();
            if (tabDisplayName != null) {
                // 如果有专门的Tab显示名称，使用它
                coloredName = Utils.getStringFromFormattedCharSequence(tabDisplayName.getVisualOrderText());
            } else {
                // 如果没有专门的Tab显示名称，使用团队格式化的名称
                coloredName = Utils.getStringFromFormattedCharSequence(
            PlayerTeam.formatNameForTeam(
                playerInfo.getTeam(),
                Component.literal(playerInfo.getProfile().getName())
            ).getVisualOrderText()
        );
            }
        }

        // 保持与NameProtect功能的兼容性
        NameProtect nameProtect = (NameProtect) Main.INSTANCE.moduleManager.getModule("NameProtect");
        if (nameProtect.isEnable() && mc.player != null && coloredName.contains(mc.player.getName().getString())) {
            coloredName = coloredName.replace(mc.player.getName().getString(), "Hide");
        }

        return coloredName;
    }

    private void drawColoredText(CanvasStack canvasStack, String text, float x, float y, boolean shadow) {
        SkiaRender.drawColoredText(canvasStack, text, x, y, 14, shadow);
    }

    private void drawColoredText(CanvasStack canvasStack, String text, float x, float y, boolean shadow, int fontSize) {
        SkiaRender.drawColoredText(canvasStack, text, x, y, fontSize, shadow);
    }

    private float getCleanTextWidth(String text) {
        return SkiaRender.getCleanTextWidth(text, 14);
    }

    private float getCleanTextWidth(String text, int fontSize) {
        return SkiaRender.getCleanTextWidth(text, fontSize);
    }






    private String getServerMOTD() {
        if (mc.getCurrentServer() != null) {
            // 不再使用stripFormatting，保留原始的颜色信息
            return Utils.getStringFromFormattedCharSequence(mc.getCurrentServer().motd.getVisualOrderText());
    }
        return "";
    }

    // 添加获取带颜色的服务器信息的方法
    private String getColoredServerInfo() {
        if (mc.getCurrentServer() != null) {
            return mc.getCurrentServer().ip;
        }
        if (mc.hasSingleplayerServer()) {
            return "§eSingleplayer"; // 使用黄色
        }
        return "§cUnknown Server"; // 使用红色表示未知服务器
    }

    // 添加获取带颜色的玩家数量的方法
    private String getColoredPlayerCount() {
        List<PlayerInfo> players = getPlayerInfos();
        if (mc.getCurrentServer() != null) {
            ServerData serverData = mc.getCurrentServer();
            if (serverData.players != null) {
                return "§a" + serverData.players.online() + "§f/§a" + serverData.players.max(); // 在线人数绿色，分隔符白色
            }
        }
        return "§a" + players.size() + " players"; // 绿色
    }





    @Override
    public void left(CanvasStack canvasStack, float x, float y, float width, float height) {
        List<PlayerInfo> players = getPlayerInfos();

        String serverInfo = getColoredServerInfo();
        String serverMOTD = getServerMOTD();
        String playerCount = getColoredPlayerCount();

        float currentY = 12;

        // 渲染服务器信息（带颜色）
        float serverInfoWidth = getCleanTextWidth(serverInfo, 16);
        float serverInfoX = (width - serverInfoWidth) / 2f;
        drawColoredText(canvasStack, serverInfo, serverInfoX, currentY, false, 16);
        currentY += 16;

        // 渲染MOTD（带颜色）
        if (!serverMOTD.isEmpty()) {
            String truncatedMOTD = truncateText(serverMOTD, 50);
            float motdWidth = getCleanTextWidth(truncatedMOTD, 12);
            float motdX = (width - motdWidth) / 2f;
            drawColoredText(canvasStack, truncatedMOTD, motdX, currentY, false, 12);
        }

        currentY += 12;
        // 渲染玩家数量（带颜色）
        float playerCountWidth = getCleanTextWidth(playerCount, 14);
        float playerCountX = (width - playerCountWidth) / 2f;
        drawColoredText(canvasStack, playerCount, playerCountX, currentY, false, 14);

        if (players.isEmpty()) return;

        int playersPerColumn = playersPerColumn();
        int maxColumns = maxColumns();
        int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));

        int actualPlayersPerColumn = (int) Math.ceil(players.size() / (double) columns);

        float maxColumnWidth = 0;
        for (PlayerInfo playerInfo : players) {
            String displayName = getDisplayName(playerInfo);
            // 使用getCleanTextWidth方法计算宽度
            maxColumnWidth = Math.max(maxColumnWidth, 16 + getCleanTextWidth(displayName + " " + playerInfo.getLatency() + "ms"));
        }

        float totalContentWidth;
        if (columns == 1) {
            totalContentWidth = maxColumnWidth;
        } else {
            totalContentWidth = columns * maxColumnWidth + (columns - 1) * 6;
        }

        float startX = (width - totalContentWidth) / 2f;

        int playerIndex = 0;
        for (int col = 0; col < columns; col++) {
            for (int row = 0; row < Math.min(actualPlayersPerColumn, players.size() - col * actualPlayersPerColumn); row++) {
                if (playerIndex >= players.size()) break;
                PlayerInfo playerInfo = players.get(playerIndex);

                try {
                    float headX = startX + col * (maxColumnWidth + 6);
                    float headY = currentY + row * 16;
                    float textX = headX + 14 + 2;
                    float textY = headY + 11;

                    SkiaRender.drawTabPlayerHead(canvasStack, playerInfo, headX, headY + 9, 14, 14, 2);

                    String displayName = truncateText(getDisplayName(playerInfo), 18);

                    // 使用drawColoredText来渲染带颜色的玩家名字
                    drawColoredText(canvasStack, displayName, textX, textY, false);

                    // 使用getCleanTextWidth方法计算名字宽度
                    float nameWidth = getCleanTextWidth(displayName);
                    Color pingColor = getPingColor(playerInfo);
                    SkiaFontManager.getDefaultFont12().drawText(canvasStack, " " + playerInfo.getLatency() + "ms", textX + nameWidth, textY, pingColor.getRGB());

                    playerIndex++;
                } catch (Exception e) {
                    playerIndex++;
                }
            }
        }
    }

    private Color getPingColor(PlayerInfo playerInfo) {
        int ping = playerInfo.getLatency();
        if (ping < 50) {
            return new Color(85, 255, 85);
        } else if (ping < 100) {
            return new Color(255, 255, 85);
        } else if (ping < 200) {
            return new Color(255, 170, 85);
        } else {
            return new Color(255, 85, 85);
        }
    }

    private Color getGameModeColor(PlayerInfo playerInfo) {
        return switch (playerInfo.getGameMode()) {
            case SPECTATOR -> new Color(128, 128, 128);
            case CREATIVE -> new Color(255, 215, 0);
            case SURVIVAL -> new Color(255, 255, 255);
            case ADVENTURE -> new Color(0, 255, 0);
        };
    }

    @Override
    public void right(CanvasStack canvasStack, float x, float y, float width, float height) {}
}

