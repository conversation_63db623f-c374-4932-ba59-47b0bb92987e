package com.leave.ink.ui.skija.utils;

import com.leave.ink.Main;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

import java.util.function.Consumer;

/**
 * Skia3DRenderer - 3D空间中的2D渲染工具类
 * 
 * 这个类提供了在3D世界空间中使用Skia进行2D渲染的能力
 * 主要用于NameTags、ESP等需要在实体上方显示2D内容的模块
 * 
 * 核心原理：
 * 1. 在3D空间中建立以目标为中心的局部坐标系
 * 2. 通过Billboard变换让2D内容始终面向相机
 * 3. 使用投影变换获取屏幕坐标
 * 4. 在正确的位置使用Skia进行2D渲染
 */
public class Skia3DRenderer implements IMinecraft {
    
    private static CanvasStack cachedCanvasStack = null;
    private static long lastCanvasUpdate = 0;
    private static final long CANVAS_CACHE_TIME = 100;
    
    /**
     * 3D渲染上下文 - 包含渲染所需的所有信息
     */
    public static class Render3DContext {
        public final PoseStack poseStack;
        public final Entity targetEntity;
        public final Vec3 worldPosition;
        public final Vec3 screenPosition;
        public final float scale;
        public final float distance;
        public final CanvasStack canvasStack;
        
        public Render3DContext(PoseStack poseStack, Entity targetEntity, Vec3 worldPosition, 
                              Vec3 screenPosition, float scale, float distance, CanvasStack canvasStack) {
            this.poseStack = poseStack;
            this.targetEntity = targetEntity;
            this.worldPosition = worldPosition;
            this.screenPosition = screenPosition;
            this.scale = scale;
            this.distance = distance;
            this.canvasStack = canvasStack;
        }
    }
    
    /**
     * 渲染配置类
     */
    public static class RenderConfig {
        public float baseScale = 1.5f;
        public float yOffset = 0.55f;  // 实体头顶偏移
        public float minDistance = 1.0f;
        public float scaleMultiplier = 0.25f;
        public boolean enableDistanceScaling = true;
        public boolean enableBillboard = true;
        
        public RenderConfig() {}
        
        public RenderConfig setBaseScale(float baseScale) {
            this.baseScale = baseScale;
            return this;
        }
        
        public RenderConfig setYOffset(float yOffset) {
            this.yOffset = yOffset;
            return this;
        }
        
        public RenderConfig setMinDistance(float minDistance) {
            this.minDistance = minDistance;
            return this;
        }
        
        public RenderConfig setScaleMultiplier(float scaleMultiplier) {
            this.scaleMultiplier = scaleMultiplier;
            return this;
        }
        
        public RenderConfig setEnableDistanceScaling(boolean enableDistanceScaling) {
            this.enableDistanceScaling = enableDistanceScaling;
            return this;
        }
        
        public RenderConfig setEnableBillboard(boolean enableBillboard) {
            this.enableBillboard = enableBillboard;
            return this;
        }
    }
    
    /**
     * 在3D空间中渲染2D内容的主要方法
     * 
     * @param entity 目标实体
     * @param poseStack 变换矩阵栈
     * @param config 渲染配置
     * @param renderer 渲染回调函数
     * @return 是否成功渲染
     */
    public static boolean render3D(Entity entity, PoseStack poseStack, RenderConfig config, 
                                  Consumer<Render3DContext> renderer) {
        if (entity == null || poseStack == null || renderer == null) {
            return false;
        }
        
        // 获取Skia画布
        CanvasStack canvasStack = getCachedSkiaCanvas();
        if (canvasStack == null) {
            return false;
        }
        
        // 计算世界位置
        Vec3 worldPos = calculateWorldPosition(entity, config.yOffset);
        
        // 计算屏幕位置
        Vec3 screenPos = projectToScreen(worldPos);
        if (screenPos == null || !SkiaCompatLayer.isOnScreen(screenPos)) {
            return false;
        }
        
        // 计算距离和缩放
        float distance = mc.player.distanceTo(entity);
        float scale = calculateScale(distance, config);
        
        // 保存当前变换状态
        poseStack.pushPose();
        
        try {
            // 应用3D变换
            apply3DTransform(entity, poseStack, config, scale);
            
            // 创建渲染上下文
            Render3DContext context = new Render3DContext(
                poseStack, entity, worldPos, screenPos, scale, distance, canvasStack
            );
            
            // 执行渲染回调
            renderer.accept(context);
            
            return true;
            
        } catch (Exception e) {
            System.err.println("Skia3DRenderer error: " + e.getMessage());
            return false;
        } finally {
            // 恢复变换状态
            poseStack.popPose();
        }
    }
    
    /**
     * 简化版本 - 使用默认配置
     */
    public static boolean render3D(Entity entity, PoseStack poseStack, Consumer<Render3DContext> renderer) {
        return render3D(entity, poseStack, new RenderConfig(), renderer);
    }
    
    /**
     * 计算实体的世界位置
     */
    private static Vec3 calculateWorldPosition(Entity entity, float yOffset) {
        float partialTicks = mc.getFrameTime();
        double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
        double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + yOffset;
        double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;
        
        return new Vec3(entityX, entityY, entityZ);
    }
    
    /**
     * 应用3D变换（平移、旋转、缩放）
     */
    private static void apply3DTransform(Entity entity, PoseStack poseStack, RenderConfig config, float scale) {
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
        float partialTicks = mc.getFrameTime();
        
        // 1. 平移到实体位置
        poseStack.translate(
            entity.xOld + (entity.getX() - entity.xOld) * partialTicks - renderManager.camera.getPosition().x,
            entity.yOld + (entity.getY() - entity.yOld) * partialTicks - renderManager.camera.getPosition().y + entity.getEyeHeight() + config.yOffset,
            entity.zOld + (entity.getZ() - entity.zOld) * partialTicks - renderManager.camera.getPosition().z
        );
        
        // 2. Billboard变换 - 让2D内容始终面向相机
        if (config.enableBillboard) {
            poseStack.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
            poseStack.mulPose(Axis.XP.rotationDegrees(renderManager.camera.getXRot()));
        }
        
        // 3. 应用缩放
        poseStack.scale(-scale, -scale, scale);
    }
    
    /**
     * 计算基于距离的缩放值
     */
    private static float calculateScale(float distance, RenderConfig config) {
        if (!config.enableDistanceScaling) {
            return config.baseScale;
        }
        
        float adjustedDistance = distance * config.scaleMultiplier;
        if (adjustedDistance < config.minDistance) {
            adjustedDistance = config.minDistance;
        }
        
        return adjustedDistance / 100f * config.baseScale;
    }
    
    /**
     * 将世界坐标投影到屏幕坐标
     */
    private static Vec3 projectToScreen(Vec3 worldPos) {
        try {
            if (Main.INSTANCE.projection == null) {
                return null;
            }
            
            EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
            Camera camera = renderManager.camera;
            
            Vec3 relativePos = new Vec3(
                worldPos.x - camera.getPosition().x,
                worldPos.y - camera.getPosition().y,
                worldPos.z - camera.getPosition().z
            );
            
            return Main.INSTANCE.projection.projectToScreen(relativePos);
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取缓存的Skia画布
     */
    private static CanvasStack getCachedSkiaCanvas() {
        long currentTime = System.currentTimeMillis();
        if (cachedCanvasStack != null && (currentTime - lastCanvasUpdate) < CANVAS_CACHE_TIME) {
            return cachedCanvasStack;
        }
        
        try {
            if (SkiaProcess.INSTANCE == null ||
                SkiaProcess.INSTANCE.skiaUtils == null ||
                SkiaProcess.INSTANCE.skiaUtils.surface == null ||
                SkiaProcess.INSTANCE.skiaUtils.canvas == null) {
                cachedCanvasStack = null;
                return null;
            }
            
            cachedCanvasStack = new CanvasStack(SkiaProcess.INSTANCE.skiaUtils);
            lastCanvasUpdate = currentTime;
            return cachedCanvasStack;
            
        } catch (Exception e) {
            cachedCanvasStack = null;
            return null;
        }
    }
    
    /**
     * 获取缓存的Skia画布（公共方法，供其他模块使用）
     */
    public static CanvasStack getSkiaCanvas() {
        long currentTime = System.currentTimeMillis();
        if (cachedCanvasStack != null && (currentTime - lastCanvasUpdate) < CANVAS_CACHE_TIME) {
            return cachedCanvasStack;
        }

        try {
            if (SkiaProcess.INSTANCE == null ||
                SkiaProcess.INSTANCE.skiaUtils == null ||
                SkiaProcess.INSTANCE.skiaUtils.surface == null ||
                SkiaProcess.INSTANCE.skiaUtils.canvas == null) {
                cachedCanvasStack = null;
                return null;
            }

            cachedCanvasStack = new CanvasStack(SkiaProcess.INSTANCE.skiaUtils);
            lastCanvasUpdate = currentTime;
            return cachedCanvasStack;

        } catch (Exception e) {
            cachedCanvasStack = null;
            return null;
        }
    }

    /**
     * 清理缓存
     */
    public static void clearCache() {
        cachedCanvasStack = null;
    }
}
