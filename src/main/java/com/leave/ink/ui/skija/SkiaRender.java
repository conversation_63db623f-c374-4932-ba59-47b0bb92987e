package com.leave.ink.ui.skija;

import com.leave.ink.ui.skija.fbo.FrameBuffers;
import com.leave.ink.ui.skija.fbo.GameFrameBuffer;
import com.leave.ink.ui.skija.utils.ImageHelper;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.file.LocalResource;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;

import com.mojang.blaze3d.platform.Lighting;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import io.github.humbleui.skija.*;
import io.github.humbleui.types.RRect;
import io.github.humbleui.types.Rect;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.ChatFormatting;

import net.minecraft.resources.ResourceLocation;

import net.minecraft.world.entity.player.Player;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.world.item.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;


public class SkiaRender implements IMinecraft {

    private static final Map<String, Image> images = new HashMap<>();
    public static final String LOGO_CHAR = "1";
    
    public static void drawRect(CanvasStack canvasStack, float x, float y, float width, float height, int color) {
        Paint redPaint = new Paint().setColor(color);
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Rect rect = Rect.makeXYWH(x, y, width, height);
        canvasStack.canvas.drawRect(rect, redPaint);
    }
    public static void scissorRect(CanvasStack canvasStack, float x, float y, float width, float height,ClipMode clipMode) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.canvas.clipRect(
                Rect.makeXYWH(x,y,width,height),
                clipMode
        );
    }
    public static void drawCircle(CanvasStack canvasStack, float x, float y, float radius, int color) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        Paint paint = new Paint();
        paint.setColor(color);
        paint.setAntiAlias(true);

        // 正确的坐标转换，与其他方法保持一致
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        radius = SkiaUtils.transformCoord(radius);

        canvasStack.canvas.drawCircle(x, y, radius, paint);
        paint.close();
    }
    public static void scissorRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height,float radius,ClipMode clipMode) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.canvas.clipRRect(
                RRect.makeXYWH(x,y,width,height,radius),
                clipMode
        );
    }

    public static void drawImage(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height,boolean blend, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if(bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Image image = images.get(imageLocal);

        if(image == null) {
            image = Image.makeDeferredFromEncodedBytes(bytes);
            images.put(imageLocal, image);
        }

        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setColor(color);
        if(blend)
            paint.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000)); // 保留所有颜色，减去黑色
        paint.setAntiAlias(true);
        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
    }

    public static void drawImage(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height) {
        drawImage(canvasStack, imageLocal, x, y, width, height, true, -1);
    }

    public static void drawImage(CanvasStack canvasStack, Image image, float x, float y, float width, float height) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
    }

    public static void drawImageIntersect(CanvasStack canvasStack, Image image, float x, float y, float width, float height, float radius) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.push();
        boolean round = radius != 0;
        canvasStack.push();
        if(round)
            canvasStack.canvas.clipRRect(RRect.makeXYWH(x,y,width,height,radius), ClipMode.INTERSECT);
        else
            canvasStack.canvas.clipRect(Rect.makeXYWH(x,y,width,height), ClipMode.INTERSECT);

//        canvasStack.canvas.clipRect(Rect.makeXYWH(x, y, width, height), ClipMode.INTERSECT);
        canvasStack.canvas.drawImage(image, x, y);
        canvasStack.pop();
    }

    public static void drawImageIntersect(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height, boolean blend, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if(bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }

        Image image = images.get(imageLocal);

        if(image == null) {
            image = Image.makeDeferredFromEncodedBytes(bytes);
            images.put(imageLocal, image);
        }

        Paint paint = new Paint();
        if(blend) {
            paint.setColor(color);
            paint.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000)); // 保留所有颜色，减去黑色
        }
        paint.setAntiAlias(true);

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        canvasStack.push();
        canvasStack.canvas.clipRect(Rect.makeXYWH(x, y, width, height), ClipMode.INTERSECT);

        Rect dst = Rect.makeXYWH(x, y, width, height);
        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
        canvasStack.pop();
    }
    
    public static void drawPlayerHead(CanvasStack canvasStack, Player player, float x, float y, float width, float height, float cornerRadius) {
        if (player == null || canvasStack.canvas == null) {
            drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
            return;
        }

        try {
            ResourceLocation skinLocation = null;
            try {
                if (player instanceof AbstractClientPlayer) {
                    skinLocation = ((AbstractClientPlayer) player).getSkinTextureLocation();
                }
            } catch (Exception e) {
                System.err.println("获取玩家皮肤位置失败: " + e.getMessage());
            }
            if (skinLocation == null) {
                drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            float scaledX = SkiaUtils.transformCoord(x);
            float scaledY = SkiaUtils.transformCoord(y);
            float scaledWidth = SkiaUtils.transformCoord(width);
            float scaledHeight = SkiaUtils.transformCoord(height);
            Image headImage = ImageHelper.extractRegion(skinLocation, 8, 8, 8, 8);
            if (headImage == null || headImage.isClosed()) {
                drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            Image hatImage = ImageHelper.extractRegion(skinLocation, 40, 8, 8, 8);
            RRect clipRect = RRect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight, cornerRadius);
            Paint paint = new Paint();
            try (paint) {
                paint.setAntiAlias(true);
                canvasStack.push();
                canvasStack.canvas.clipRRect(clipRect, ClipMode.INTERSECT);
                canvasStack.canvas.drawImageRect(
                        headImage,
                        Rect.makeXYWH(0, 0, headImage.getWidth(), headImage.getHeight()),
                        Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                        paint
                );
                if (hatImage != null && !hatImage.isClosed()) {
                    Paint hatPaint = new Paint();
                    hatPaint.setAntiAlias(true);
                    hatPaint.setBlendMode(BlendMode.SRC_OVER);

                    canvasStack.canvas.drawImageRect(
                            hatImage,
                            Rect.makeXYWH(0, 0, hatImage.getWidth(), hatImage.getHeight()),
                            Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                            hatPaint
                    );

                    hatPaint.close();
                }
            } finally {
                canvasStack.pop();
            }
        } catch (Exception e) {
            System.err.println("绘制玩家头像失败: " + e.getMessage());
            e.printStackTrace();
            drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
        }
    }


    /**
     * 绘制错误头像，在无法加载正常头像时使用
     */
    private static void drawErrorHead(CanvasStack canvasStack, float x, float y, float width, float height, float radius) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        try {
            x = SkiaUtils.transformCoord(x);
            y = SkiaUtils.transformCoord(y);
            width = SkiaUtils.transformCoord(width);
            height = SkiaUtils.transformCoord(height);
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(0xFFFF00FF);
            RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
            canvasStack.canvas.drawRRect(rrect, backgroundPaint);
            Paint gridPaint = new Paint();
            gridPaint.setColor(0xFF000000);
            gridPaint.setStrokeWidth(1.0f);
            gridPaint.setMode(PaintMode.STROKE);
            gridPaint.setAntiAlias(true);
            float gridSize = Math.min(width, height) / 4;
            if (gridSize < 1) gridSize = 1;
            for (int i = 0; i <= 4; i++) {
                float xPos = x + i * gridSize;
                float yPos = y + i * gridSize;
                if (xPos <= x + width) {
                    canvasStack.canvas.drawLine(xPos, y, xPos, y + height, gridPaint);
                }
                if (yPos <= y + height) {
                    canvasStack.canvas.drawLine(x, yPos, x + width, yPos, gridPaint);
                }
            }

            if (width >= 8 && height >= 8) {
                Paint crossPaint = new Paint();
                crossPaint.setColor(0xFFFFFFFF);
                crossPaint.setAntiAlias(true);
                crossPaint.setStrokeWidth(2.0f);
                canvasStack.canvas.drawLine(x + width * 0.25f, y + height * 0.25f,
                        x + width * 0.75f, y + height * 0.75f, crossPaint);
                canvasStack.canvas.drawLine(x + width * 0.75f, y + height * 0.25f,
                        x + width * 0.25f, y + height * 0.75f, crossPaint);

                crossPaint.close();
            }

            backgroundPaint.close();
            gridPaint.close();

        } catch (Exception e) {
            System.err.println("绘制错误头像失败: " + e.getMessage());
            try {
                Paint fallbackPaint = new Paint().setColor(0xFFFF00FF);
                canvasStack.canvas.drawRect(Rect.makeXYWH(x, y, width, height), fallbackPaint);
                fallbackPaint.close();
            } catch (Exception ignored) {
            }
        }
    }
    public static void drawCircularProgress(CanvasStack canvasStack, float centerX, float centerY, float radius, float strokeWidth, float progress, int backgroundColor, int progressColor) {
        centerX = SkiaUtils.transformCoord(centerX);
        centerY = SkiaUtils.transformCoord(centerY);
        radius = SkiaUtils.transformCoord(radius);
        strokeWidth= SkiaUtils.transformCoord(strokeWidth);
        Paint paint = new Paint().setAntiAlias(true).setStrokeWidth(strokeWidth).setStroke(true);
        float left = centerX - radius;
        float top = centerY - radius;
        float right = centerX + radius;
        float bottom = centerY + radius;

        paint.setColor(backgroundColor);
        canvasStack.getCanvas().drawArc(left, top, right, bottom, 0, 360, false, paint);

        paint.setColor(progressColor);
        float sweepAngle = 360f * progress;
        canvasStack.getCanvas().drawArc(left, top, right, bottom, -90, sweepAngle, false, paint); // -90 是从顶部开始
    }

    public static void drawRoundedImage(CanvasStack canvasStack, Image image, float x, float y, float width, float height, float radius) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        canvasStack.push();

        RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
        canvasStack.canvas.clipRRect(rrect, ClipMode.INTERSECT);

        canvasStack.canvas.drawImageRect(
                image,
                Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                dst,
                paint
        );

        canvasStack.pop();
    }



    public static void drawRoundedRectStroke(CanvasStack canvasStack,float x, float y, float width, float height, float radius, int color) {
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setMode(PaintMode.STROKE);
        paint.setStrokeWidth(1.0f);
        paint.setAntiAlias(true);
        paint.setStrokeCap(PaintStrokeCap.ROUND);
        paint.setStrokeJoin(PaintStrokeJoin.ROUND);
        x = Math.round(SkiaUtils.transformCoord(x));
        y = Math.round(SkiaUtils.transformCoord(y));
        width = Math.round(SkiaUtils.transformCoord(width));
        height = Math.round(SkiaUtils.transformCoord(height));

        RRect rect = RRect.makeXYWH(x, y, width, height, radius);
        canvasStack.canvas.drawRRect(rect, paint);
    }

    public static void drawRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height, float radius, int color) {
        Paint paint = new Paint()
                .setColor(color)
                .setAntiAlias(true);

        float transformedX = Math.round(SkiaUtils.transformCoord(x));
        float transformedY = Math.round(SkiaUtils.transformCoord(y));
        float transformedWidth = Math.round(SkiaUtils.transformCoord(width));
        float transformedHeight = Math.round(SkiaUtils.transformCoord(height));
//        paint.setImageFilter(
//                ImageFilter.makeBlur(1.1f, 1.1f, FilterTileMode.DECAL)
//        );
        canvasStack.canvas.drawRRect(RRect.makeXYWH(transformedX, transformedY, transformedWidth, transformedHeight, radius), paint);
    }

    public static void drawRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height,
                                       float topLeftRadius, float topRightRadius, float bottomRightRadius, float bottomLeftRadius,
                                       int color) {
        Paint redPaint = new Paint().setColor(color);
        redPaint.setImageFilter(ImageFilter.makeBlur(.5f, .5f, FilterTileMode.DECAL));

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        float[] radii = {
                topLeftRadius, topLeftRadius,
                topRightRadius, topRightRadius,
                bottomRightRadius, bottomRightRadius,
                bottomLeftRadius, bottomLeftRadius
        };

        RRect rect = RRect.makeComplexXYWH(x, y, width, height, radii);
        canvasStack.canvas.drawRRect(rect, redPaint);
    }

    private static final Paint ROUNDED_RECT_PAINT2 = new Paint().setAntiAlias(false);
    public static void drawBlurRect(CanvasStack canvasStack,float x, float y, float width, float height, float radius, float blurRadius) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Image image = ImageHelper.getMinecraftAsImage(canvasStack.context, mc.getMainRenderTarget().getColorTextureId(),
                mc.getMainRenderTarget().width,
                mc.getMainRenderTarget().height,
                SurfaceOrigin.BOTTOM_LEFT, false);
        Paint blurPaint = ROUNDED_RECT_PAINT2;
        blurPaint.setImageFilter(
                ImageFilter.makeBlur(blurRadius, blurRadius, FilterTileMode.DECAL)
        );
        boolean round = radius != 0;
        canvasStack.push();
        if(round)
            canvasStack.canvas.clipRRect(RRect.makeXYWH(x,y,width,height,radius), ClipMode.INTERSECT);
        else
            canvasStack.canvas.clipRect(Rect.makeXYWH(x,y,width,height), ClipMode.INTERSECT);

        canvasStack.resetTransform();
        canvasStack.canvas.drawImage(image, 0f,0f,blurPaint);
        canvasStack.pop();
    }

    public static void drawRoundedRectWithShadow(CanvasStack canvasStack, float x, float y, float w, float h, float radius) {
        x = Math.round(SkiaUtils.transformCoord(x));
        y = Math.round(SkiaUtils.transformCoord(y));
        w = Math.round(SkiaUtils.transformCoord(w));
        h = Math.round(SkiaUtils.transformCoord(h));
        Paint outerShadow = new Paint()
                .setColor(new java.awt.Color(0, 0, 0, 50).getRGB())
                .setAntiAlias(true)
                .setImageFilter(ImageFilter.makeBlur(20f, 20f, FilterTileMode.DECAL));
        canvasStack.canvas.drawRRect(RRect.makeXYWH(x - 3, y - 3, w + 6, h + 6, radius + 3), outerShadow);
        Paint innerShadow = new Paint()
                .setColor(new java.awt.Color(0, 0, 0, 100).getRGB())
                .setAntiAlias(true)
                .setImageFilter(ImageFilter.makeBlur(6f, 6f, FilterTileMode.DECAL));
        canvasStack.canvas.drawRRect(RRect.makeXYWH(x + 2, y + 2, w, h, radius), innerShadow);
    }

    private static final ConcurrentHashMap<String, Image> imageCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Image> scaledImageCache = new ConcurrentHashMap<>();

    private static final Paint basePaint = createBasePaint();

    private static Paint createBasePaint() {
        Paint p = new Paint();
        p.setAntiAlias(true);
        p.setColor(0xFFFFFFFF);
        p.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000));
        return p;
    }

    public static void drawHighQualityLogo(CanvasStack canvasStack, String imageLocal,
                                           float x, float y, float width, float height, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if (bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Image image = imageCache.computeIfAbsent(imageLocal, key -> {
            try {
                return Image.makeDeferredFromEncodedBytes(bytes);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        });

        if (image == null || image.isClosed()) {
            return;
        }

        float scaleFactor = Math.min(width / image.getWidth(), height / image.getHeight());

        canvasStack.push();

        if (scaleFactor < 0.5f) {
            int tempWidth = (int) Math.min(width * 2, 1024);
            int tempHeight = (int) Math.min(height * 2, 1024);

            String cacheKey = imageLocal + "_scaled_" + tempWidth + "x" + tempHeight;
            Image scaledImage = scaledImageCache.get(cacheKey);

            if (scaledImage == null || scaledImage.isClosed()) {
                try (Surface tempSurface = Surface.makeRasterN32Premul(tempWidth, tempHeight)) {
                    tempSurface.getCanvas().drawImageRect(
                            image,
                            Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                            Rect.makeXYWH(0, 0, tempWidth, tempHeight),
                            SamplingMode.CATMULL_ROM,
                            basePaint,
                            true
                    );
                    scaledImage = tempSurface.makeImageSnapshot();
                    if (scaledImage != null) {
                        scaledImageCache.put(cacheKey, scaledImage);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (scaledImage != null && !scaledImage.isClosed()) {
                Paint paint = basePaint;
                paint.setColor(color);
                canvasStack.canvas.drawImageRect(
                        scaledImage,
                        Rect.makeXYWH(0, 0, tempWidth, tempHeight),
                        Rect.makeXYWH(x, y, width, height),
                        new FilterMipmap(FilterMode.LINEAR, MipmapMode.LINEAR),
                        paint,
                        true
                );
            }
        } else {
            Paint paint = basePaint;
            paint.setColor(color);

            Rect dst = Rect.makeXYWH(x, y, width, height);
            canvasStack.canvas.drawImageRect(
                    image,
                    Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                    dst,
                    SamplingMode.CATMULL_ROM,
                    paint,
                    true
            );
        }

        canvasStack.pop();
    }

    private static final ConcurrentHashMap<Integer, com.leave.ink.ui.skija.font.SkiaFont> logoFontCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Integer, com.leave.ink.ui.skija.font.SkiaFont> iconsFontCache = new ConcurrentHashMap<>();

    public static void drawTTFLogo(CanvasStack canvasStack, String logoChar,
                                   float x, float y, float size, int color) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        if (logoChar == null || logoChar.isEmpty()) {
            logoChar = LOGO_CHAR;
        }

        try {
            int fontSize = Math.round(size);
            com.leave.ink.ui.skija.font.SkiaFont logoFont = logoFontCache.computeIfAbsent(fontSize,
                s -> com.leave.ink.ui.skija.font.SkiaFont.createSkiaFont("logo.ttf", s));
            logoFont.drawText(canvasStack, logoChar, x, y, color);

        } catch (Exception e) {
            drawCircle(canvasStack, x + size/2, y + size/2, size/2, color);
        }
    }

    public static void drawIconsFont(CanvasStack canvasStack, String iconChar,
                                     float x, float y, float size, int color) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        if (iconChar == null || iconChar.isEmpty()) {
            return;
        }

        try {
            int fontSize = Math.round(size);
            com.leave.ink.ui.skija.font.SkiaFont iconsFont = iconsFontCache.computeIfAbsent(fontSize,
                s -> com.leave.ink.ui.skija.font.SkiaFont.createSkiaFont("icons2.ttf", s));
            iconsFont.drawText(canvasStack, iconChar, x, y, color);

        } catch (Exception e) {
            drawCircle(canvasStack, x + size/2, y + size/2, size/4, color);
        }
    }

    public static float getIconsFontWidth(String iconChar, float size) {
        if (iconChar == null || iconChar.isEmpty()) {
            return 0;
        }

        try {
            int fontSize = Math.round(size);
            com.leave.ink.ui.skija.font.SkiaFont iconsFont = iconsFontCache.computeIfAbsent(fontSize,
                s -> com.leave.ink.ui.skija.font.SkiaFont.createSkiaFont("icons2.ttf", s));
            return iconsFont.getWidth(iconChar);

        } catch (Exception e) {
            return size;
        }
    }

    public static void drawTTFLogo(CanvasStack canvasStack, float x, float y, float size, int color) {
        drawTTFLogo(canvasStack, LOGO_CHAR, x, y, size, color);
    }

    public static void drawRoundedEdgeLine(CanvasStack canvasStack, float x, float startY, float length, float radius, int color, boolean isLeft) {
        x = Math.round(SkiaUtils.transformCoord(x));
        startY = Math.round(SkiaUtils.transformCoord(startY));
        length = Math.round(SkiaUtils.transformCoord(length));
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setMode(PaintMode.STROKE);
        paint.setStrokeWidth(1.0f);
        paint.setAntiAlias(true);
        paint.setStrokeCap(PaintStrokeCap.ROUND);
        paint.setStrokeJoin(PaintStrokeJoin.ROUND);
        canvasStack.canvas.drawLine(x, startY, x, startY + length, paint);
        paint.close();
    }

    public static void drawTabPlayerHead(CanvasStack canvasStack, PlayerInfo playerInfo, float x, float y, float width, float height, float cornerRadius) {
        if (playerInfo == null || canvasStack.canvas == null) {
            drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
            return;
        }

        try {
            ResourceLocation skinLocation = null;
            try {
                skinLocation = playerInfo.getSkinLocation();
            } catch (Exception e) {
                System.err.println("从PlayerInfo获取皮肤位置失败: " + e.getMessage());
            }
            
            if (skinLocation == null) {
                drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            
            float scaledX = SkiaUtils.transformCoord(x);
            float scaledY = SkiaUtils.transformCoord(y);
            float scaledWidth = SkiaUtils.transformCoord(width);
            float scaledHeight = SkiaUtils.transformCoord(height);
            
            Image headImage = ImageHelper.extractRegion(skinLocation, 8, 8, 8, 8);
            if (headImage == null || headImage.isClosed()) {
                drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            
            Image hatImage = ImageHelper.extractRegion(skinLocation, 40, 8, 8, 8);
            RRect clipRect = RRect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight, cornerRadius);
            
            Paint paint = new Paint();
            try (paint) {
                paint.setAntiAlias(true);
                canvasStack.push();
                canvasStack.canvas.clipRRect(clipRect, ClipMode.INTERSECT);
                canvasStack.canvas.drawImageRect(
                        headImage,
                        Rect.makeXYWH(0, 0, headImage.getWidth(), headImage.getHeight()),
                        Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                        paint
                );
                if (hatImage != null && !hatImage.isClosed()) {
                    Paint hatPaint = new Paint();
                    hatPaint.setAntiAlias(true);
                    hatPaint.setBlendMode(BlendMode.SRC_OVER);

                    canvasStack.canvas.drawImageRect(
                            hatImage,
                            Rect.makeXYWH(0, 0, hatImage.getWidth(), hatImage.getHeight()),
                            Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                            hatPaint
                    );

                    hatPaint.close();
                }
            } finally {
                canvasStack.pop();
            }
        } catch (Exception e) {
            System.err.println("绘制TabOverlay玩家头像失败: " + e.getMessage());
            e.printStackTrace();
            drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
        }
    }
    private static void drawDefaultHead(CanvasStack canvasStack, float x, float y, float width, float height, float radius) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        try {
            x = SkiaUtils.transformCoord(x);
            y = SkiaUtils.transformCoord(y);
            width = SkiaUtils.transformCoord(width);
            height = SkiaUtils.transformCoord(height);
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(0xFF4A4A4A);
            RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
            canvasStack.canvas.drawRRect(rrect, backgroundPaint);
            Paint iconPaint = new Paint();
            iconPaint.setColor(0xFF888888);
            iconPaint.setAntiAlias(true);
            
            float centerX = x + width / 2;
            float centerY = y + height / 2;
            float iconSize = Math.min(width, height) * 0.6f;
            canvasStack.canvas.drawCircle(centerX, centerY - iconSize * 0.15f, iconSize * 0.25f, iconPaint);
            float bodyWidth = iconSize * 0.4f;
            float bodyHeight = iconSize * 0.35f;
            canvasStack.canvas.drawRect(
                Rect.makeXYWH(centerX - bodyWidth/2, centerY + iconSize * 0.1f, bodyWidth, bodyHeight), 
                iconPaint
            );

            backgroundPaint.close();
            iconPaint.close();

        } catch (Exception e) {
            System.err.println("绘制默认头像失败: " + e.getMessage());
            try {
                Paint fallbackPaint = new Paint().setColor(0xFF4A4A4A);
                canvasStack.canvas.drawRect(Rect.makeXYWH(x, y, width, height), fallbackPaint);
                fallbackPaint.close();
            } catch (Exception ignored) {
            }
        }
    }

    public static void renderItem(CanvasStack canvasStack, ItemStack itemStack, float x, float y, float size) {
        renderItem(canvasStack, itemStack, x, y, size, "ItemRender_Single");
    }

    //渲染单个物品到指定位置（带FBO名称）
    public static void renderItem(CanvasStack canvasStack, ItemStack itemStack, float x, float y, float size, String fboName) {
        if (itemStack == null || itemStack.isEmpty()) {
            return;
        }
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer(fboName);
        gameFrameBuffer.execute(eventRender2D -> {
            PoseStack poseStack = eventRender2D.getPoseStack();
            poseStack.pushPose();
            RenderSystem.enableDepthTest();
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            Lighting.setupFor3DItems();
            RenderUtils.renderItemStack(poseStack, itemStack, 0, 0);
            poseStack.popPose();
        });
        canvasStack.push();
        scissorRect(canvasStack, x, y, size, size, ClipMode.INTERSECT);
        canvasStack.canvas.save();
        canvasStack.canvas.translate(SkiaUtils.transformCoord(x), SkiaUtils.transformCoord(y));
        canvasStack.canvas.scale(SkiaUtils.transformCoord(size) / 16.0f, SkiaUtils.transformCoord(size) / 16.0f);
        gameFrameBuffer.draw(canvasStack);
        canvasStack.canvas.restore();
        canvasStack.pop();
    }
    //渲染带背景的物品槽位,borderColor边框颜色设置为0为无边框
    public static void renderItemSlot(CanvasStack canvasStack, ItemStack itemStack, float x, float y, float size, int backgroundColor, int borderColor) {
        if (backgroundColor != 0) {
            drawRoundedRect(canvasStack, x, y, size, size, 2, backgroundColor);
        }
        if (borderColor != 0) {
            drawRoundedRectStroke(canvasStack, x, y, size, size, 2, borderColor);
        }
        if (itemStack != null && !itemStack.isEmpty()) {
            float itemSize = size * 0.8f;
            float itemOffset = (size - itemSize) / 2;
            renderItem(canvasStack, itemStack, x + itemOffset, y + itemOffset, itemSize);
        }
    }
    //渲染物品网格
    public static void renderItemGrid(CanvasStack canvasStack, ItemStack[] items, float startX, float startY, float itemSize, float spacing, int columns, String fboName) {
        if (items == null || items.length == 0) {
            return;
        }
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer(fboName);
        gameFrameBuffer.execute(eventRender2D -> {
            PoseStack poseStack = eventRender2D.getPoseStack();
            poseStack.pushPose();
            RenderSystem.enableDepthTest();
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            Lighting.setupFor3DItems();
            for (int i = 0; i < items.length; i++) {
                if (items[i] != null && !items[i].isEmpty()) {
                    int row = i / columns;
                    int col = i % columns;
                    int itemX = (int)(col * (itemSize + spacing));
                    int itemY = (int)(row * (itemSize + spacing));

                    RenderUtils.renderItemStack(poseStack, items[i], itemX, itemY);
                }
            }

            poseStack.popPose();
        });
        canvasStack.push();
        float totalWidth = columns * itemSize + (columns - 1) * spacing;
        float totalHeight = ((items.length + columns - 1) / columns) * itemSize +
                ((items.length + columns - 1) / columns - 1) * spacing;

        scissorRect(canvasStack, startX, startY, totalWidth, totalHeight, ClipMode.INTERSECT);
        canvasStack.canvas.save();
        canvasStack.canvas.translate(SkiaUtils.transformCoord(startX), SkiaUtils.transformCoord(startY));
        canvasStack.canvas.scale(SkiaUtils.transformCoord(itemSize) / 16.0f, SkiaUtils.transformCoord(itemSize) / 16.0f);
        gameFrameBuffer.draw(canvasStack);
        canvasStack.canvas.restore();
        canvasStack.pop();
    }

    //简化版物品网格
    public static void renderInventoryGrid(CanvasStack canvasStack, ItemStack[] items, float startX, float startY, int columns, int rows) {
        renderInventoryGrid(canvasStack, items, startX, startY, columns, rows, "ItemGrid_Inventory");
    }
    public static void renderInventoryGrid(CanvasStack canvasStack, ItemStack[] items, float startX, float startY, int columns, int rows, String fboName) {
        if (items == null || items.length == 0) {
            return;
        }
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer(fboName);
        gameFrameBuffer.execute(eventRender2D -> {
            PoseStack poseStack = eventRender2D.getPoseStack();
            poseStack.pushPose();

            RenderSystem.enableDepthTest();
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            Lighting.setupFor3DItems();
            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < columns; col++) {
                    int index = row * columns + col;
                    if (index < items.length && items[index] != null && !items[index].isEmpty()) {
                        int itemX = col * 19;
                        int itemY = row * 20;
                        RenderUtils.renderItemStack(poseStack, items[index], itemX, itemY);
                    }
                }
            }

            poseStack.popPose();
        });
        canvasStack.push();
        float gridWidth = columns * 19;
        float gridHeight = rows * 20;

        scissorRect(canvasStack, startX, startY, gridWidth, gridHeight, ClipMode.INTERSECT);

        canvasStack.canvas.save();
        canvasStack.canvas.translate(SkiaUtils.transformCoord(startX), SkiaUtils.transformCoord(startY));

        gameFrameBuffer.draw(canvasStack);

        canvasStack.canvas.restore();
        canvasStack.pop();
    }

    /**
     * 绘制带颜色代码的文本
     * @param canvasStack 画布栈
     * @param text 包含颜色代码的文本（支持§格式）
     * @param x X坐标
     * @param y Y坐标
     * @param fontSize 字体大小（12, 14, 16等）
     * @param shadow 是否绘制阴影（当前版本暂不实现）
     */
    public static void drawColoredText(CanvasStack canvasStack, String text, float x, float y, int fontSize, boolean shadow) {
        if (text == null || text.isEmpty() || canvasStack == null || canvasStack.canvas == null) return;

        // 检查文本是否包含图标
        if (IconFont.containsIcons(text)) {
            IconFont.drawTextWithIcons(canvasStack, text, x, y, 0xFFFFFFFF, shadow);
            return;
        }

        // 根据字体大小选择对应的字体管理器
        var fontManager = switch (fontSize) {
            case 12 -> SkiaFontManager.getDefaultFont12();
            case 14 -> SkiaFontManager.getDefaultFont14();
            case 16 -> SkiaFontManager.getDefaultFont16();
            default -> SkiaFontManager.getDefaultFont14();
        };

        float currentX = x;
        int currentColor = 0xFFFFFFFF;
        StringBuilder currentSegment = new StringBuilder();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '§' && i + 1 < text.length()) {
                // 渲染当前累积的文本段
                if (!currentSegment.isEmpty()) {
                    fontManager.drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
                    currentX += fontManager.getWidth(currentSegment.toString());
                    currentSegment.setLength(0);
                }

                char colorChar = text.charAt(i + 1);

                // 处理十六进制颜色代码 (§x§r§r§g§g§b§b)
                if (colorChar == 'x' && i + 13 < text.length()) {
                    try {
                        StringBuilder hexColor = new StringBuilder();
                        for (int j = 2; j < 14; j += 2) {
                            if (text.charAt(i + j) == '§') {
                                hexColor.append(text.charAt(i + j + 1));
                            }
                        }
                        if (hexColor.length() == 6) {
                            currentColor = 0xFF000000 | Integer.parseInt(hexColor.toString(), 16);
                            i += 13;
                            continue;
                        }
                    } catch (NumberFormatException ignored) {
                    }
                }

                // 处理标准颜色代码
                ChatFormatting format = ChatFormatting.getByCode(colorChar);
                if (format != null) {
                    if (format.isColor()) {
                        Integer color = format.getColor();
                        if (color != null) {
                            currentColor = color | 0xFF000000;
                        }
                    } else if (format == ChatFormatting.RESET) {
                        currentColor = 0xFFFFFFFF;
                    }
                }
                i++; // 跳过颜色代码字符
                continue;
            }

            currentSegment.append(c);
        }

        // 渲染剩余的文本
        if (!currentSegment.isEmpty()) {
            fontManager.drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
        }
    }

    /**
     * 绘制带颜色代码的文本（默认14号字体，无阴影）
     */
    public static void drawColoredText(CanvasStack canvasStack, String text, float x, float y) {
        drawColoredText(canvasStack, text, x, y, 14, false);
    }

    /**
     * 绘制带颜色代码的文本（指定字体大小，无阴影）
     */
    public static void drawColoredText(CanvasStack canvasStack, String text, float x, float y, int fontSize) {
        drawColoredText(canvasStack, text, x, y, fontSize, false);
    }

    /**
     * 计算清理颜色代码后的文本宽度
     * @param text 包含颜色代码的文本
     * @param fontSize 字体大小
     * @return 文本宽度
     */
    public static float getCleanTextWidth(String text, int fontSize) {
        if (text == null || text.isEmpty()) return 0;
        
        String cleanText = text.replaceAll("(?i)§[0-9A-FK-OR]", "");
        return switch (fontSize) {
            case 12 -> SkiaFontManager.getDefaultFont12().getWidth(cleanText);
            case 14 -> SkiaFontManager.getDefaultFont14().getWidth(cleanText);
            case 16 -> SkiaFontManager.getDefaultFont16().getWidth(cleanText);
            default -> SkiaFontManager.getDefaultFont14().getWidth(cleanText);
        };
    }

    /**
     * 计算清理颜色代码后的文本宽度（默认14号字体）
     */
    public static float getCleanTextWidth(String text) {
        return getCleanTextWidth(text, 14);
    }

    /**
     * 绘制矩形边框
     */
    public static void drawRectOutline(CanvasStack canvasStack, float x, float y, float width, float height, float lineWidth, int color) {
        Paint paint = new Paint().setColor(color);
        paint.setMode(PaintMode.STROKE);
        paint.setStrokeWidth(SkiaUtils.transformCoord(lineWidth));
        paint.setAntiAlias(true);

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Rect rect = Rect.makeXYWH(x, y, width, height);
        canvasStack.canvas.drawRect(rect, paint);
        paint.close();
    }

    /**
     * 绘制线条
     */
    public static void drawLine(CanvasStack canvasStack, float x1, float y1, float x2, float y2, float lineWidth, int color) {
        Paint paint = new Paint().setColor(color);
        paint.setStrokeWidth(SkiaUtils.transformCoord(lineWidth));
        paint.setStrokeCap(PaintStrokeCap.ROUND);
        paint.setAntiAlias(true);

        x1 = SkiaUtils.transformCoord(x1);
        y1 = SkiaUtils.transformCoord(y1);
        x2 = SkiaUtils.transformCoord(x2);
        y2 = SkiaUtils.transformCoord(y2);

        canvasStack.canvas.drawLine(x1, y1, x2, y2, paint);
        paint.close();
    }


}