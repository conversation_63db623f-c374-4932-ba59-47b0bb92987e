# Skia3DRenderer 使用指南

## 概述

`Skia3DRenderer` 是一个专门用于在3D世界空间中进行2D渲染的工具类。它解决了在Minecraft这样的3D环境中，如何让Skia这个2D渲染引擎正确地在实体上方显示内容的问题。

## 核心原理

### 1. 混合渲染管道
- **3D定位**：在3D世界中精确定位目标实体
- **坐标变换**：建立面向相机的局部坐标系
- **投影计算**：将3D位置转换为屏幕坐标
- **2D渲染**：使用Skia在正确位置进行2D绘制

### 2. 关键技术
- **Billboard变换**：让2D内容始终面向相机
- **距离缩放**：根据距离自动调整大小
- **OpenGL状态继承**：Skia继承3D变换矩阵

## 基本使用方法

### 1. 简单使用
```java
// 最简单的使用方式
Skia3DRenderer.render3D(entity, poseStack, context -> {
    CanvasStack canvas = context.canvasStack;
    Vec3 screenPos = context.screenPosition;
    
    // 在这里进行2D渲染
    SkiaRender.drawRect(canvas, -10, -5, 20, 10, Color.RED.getRGB());
});
```

### 2. 配置参数
```java
// 创建自定义配置
Skia3DRenderer.RenderConfig config = new Skia3DRenderer.RenderConfig()
    .setBaseScale(1.5f)           // 基础缩放
    .setYOffset(0.55f)            // Y轴偏移（实体头顶）
    .setEnableDistanceScaling(true)  // 启用距离缩放
    .setEnableBillboard(true);    // 启用面向相机

// 使用配置进行渲染
Skia3DRenderer.render3D(entity, poseStack, config, context -> {
    // 渲染逻辑
});
```

### 3. 渲染上下文信息
```java
Skia3DRenderer.render3D(entity, poseStack, context -> {
    // 可用的上下文信息
    PoseStack poseStack = context.poseStack;      // 变换矩阵栈
    Entity targetEntity = context.targetEntity;   // 目标实体
    Vec3 worldPosition = context.worldPosition;   // 世界坐标
    Vec3 screenPosition = context.screenPosition; // 屏幕坐标
    float scale = context.scale;                  // 当前缩放值
    float distance = context.distance;           // 到玩家的距离
    CanvasStack canvasStack = context.canvasStack; // Skia画布
});
```

## 实际应用示例

### 1. NameTags模块
```java
private void renderWithSkia3DRenderer(LivingEntity entity, PoseStack poseStack) {
    Skia3DRenderer.RenderConfig config = new Skia3DRenderer.RenderConfig()
            .setBaseScale(scaleValue.getValue().floatValue())
            .setYOffset(0.55f)
            .setEnableDistanceScaling(true)
            .setEnableBillboard(true);

    Skia3DRenderer.render3D(entity, poseStack, config, context -> {
        CanvasStack canvasStack = context.canvasStack;
        Vec3 screenPos = context.screenPosition;
        
        // 根据UI风格选择渲染方法
        if (uiStyle.getValue().equals("Modern")) {
            renderModernSkiaUI(canvasStack, screenPos);
        } else {
            renderClassicSkiaUI(canvasStack, screenPos);
        }
    });
}
```

### 2. ESP模块
```java
private void renderSkiaESP(LivingEntity entity, PoseStack poseStack) {
    Skia3DRenderer.RenderConfig config = new Skia3DRenderer.RenderConfig()
            .setBaseScale(1.0f)
            .setYOffset(-boxHeight.getValue().floatValue() / 2f)
            .setEnableDistanceScaling(false)  // ESP不需要距离缩放
            .setEnableBillboard(false);       // ESP需要保持3D方向

    Skia3DRenderer.render3D(entity, poseStack, config, context -> {
        CanvasStack canvasStack = context.canvasStack;
        
        switch (espStyle.getValue()) {
            case "Box" -> renderSkiaBox(canvasStack, entity);
            case "Outline" -> renderSkiaOutline(canvasStack, entity);
            case "Glow" -> renderSkiaGlow(canvasStack, entity);
        }
    });
}
```

### 3. 血量显示
```java
private void renderHealthBar(LivingEntity entity, PoseStack poseStack) {
    Skia3DRenderer.render3D(entity, poseStack, context -> {
        CanvasStack canvas = context.canvasStack;
        float healthRate = HeypixelManager.getHealthRate(entity);
        
        // 背景
        SkiaRender.drawRect(canvas, -25, -3, 50, 6, 
                new Color(0, 0, 0, 120).getRGB());
        
        // 血量条
        Color healthColor = getHealthColor(healthRate);
        SkiaRender.drawRect(canvas, -25, -3, 50 * healthRate, 6, 
                healthColor.getRGB());
    });
}
```

## 配置选项详解

### RenderConfig 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `baseScale` | float | 1.5f | 基础缩放倍数 |
| `yOffset` | float | 0.55f | Y轴偏移量（实体头顶偏移） |
| `minDistance` | float | 1.0f | 最小距离（用于距离缩放） |
| `scaleMultiplier` | float | 0.25f | 距离缩放系数 |
| `enableDistanceScaling` | boolean | true | 是否启用距离缩放 |
| `enableBillboard` | boolean | true | 是否启用面向相机 |

### 使用场景建议

| 模块类型 | Billboard | 距离缩放 | Y偏移 | 说明 |
|----------|-----------|----------|-------|------|
| NameTags | ✅ | ✅ | 0.55f | 始终面向玩家，距离缩放 |
| ESP Box | ❌ | ❌ | -height/2 | 保持3D方向，固定大小 |
| 血量条 | ✅ | ✅ | 0.3f | 面向玩家，距离缩放 |
| 伤害显示 | ✅ | ✅ | 1.0f | 面向玩家，距离缩放 |

## 性能优化

### 1. 画布缓存
工具类内部自动缓存Skia画布，减少创建开销：
```java
private static CanvasStack cachedCanvasStack = null;
private static final long CANVAS_CACHE_TIME = 100; // 100ms缓存时间
```

### 2. 批量渲染
对于多个实体，建议在同一个渲染循环中处理：
```java
@EventTarget
public void onRender3D(EventRender3D event) {
    for (Entity entity : mc.level.entitiesForRendering()) {
        if (entity instanceof LivingEntity livingEntity) {
            // 使用Skia3DRenderer渲染
            Skia3DRenderer.render3D(livingEntity, event.getPoseStack(), context -> {
                // 渲染逻辑
            });
        }
    }
}
```

### 3. 条件渲染
添加必要的检查以避免不必要的渲染：
```java
// 检查实体有效性
if (!Utils.isValidEntity(entity)) return;

// 检查距离
if (mc.player.distanceTo(entity) > maxRenderDistance) return;

// 检查屏幕可见性
Vec3 screenPos = context.screenPosition;
if (!SkiaCompatLayer.isOnScreen(screenPos)) return;
```

## 错误处理

工具类内部包含完整的错误处理机制：
- 自动检查Skia上下文可用性
- 投影计算失败时返回false
- 提供回退渲染选项

```java
boolean success = Skia3DRenderer.render3D(entity, poseStack, config, renderer);
if (!success) {
    // 回退到传统渲染方式
    renderVanillaFallback(entity, poseStack);
}
```

## 总结

`Skia3DRenderer` 提供了一个强大而灵活的解决方案，让开发者能够轻松地在3D环境中使用Skia进行2D渲染。通过合理配置参数和使用模式，可以实现各种复杂的视觉效果，同时保持良好的性能和稳定性。
